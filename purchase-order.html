<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý đơn mua hộ - TRUSTMOVE</title>
    <link rel="shortcut icon" href="/images/icon.png">
    <!-- Local Assets - Tránh lỗi CORS -->
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <link rel="stylesheet" href="assets/css/select2.min.css">
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/select2.min.js"></script>
    <script src="assets/js/jspdf.umd.min.js"></script>
    <script src="assets/js/html2canvas.min.js"></script>
    <script src="assets/js/chart.min.js"></script>
    <!-- SheetJS library for Excel export -->
    <script src="assets/js/xlsx.full.min.js"></script>
    <script src="/js/api-helper.js"></script>
    <script src="/js/dashboard-processor.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        body {
            background-color: #f5f5f7;
            color: #1d1d1f;
            line-height: 2.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #d2d2d7;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .login-container h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        input, select, button {
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #d2d2d7;
            font-size: 16px;
            outline: none;
        }

        input:focus, select:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
        }

        button {
            background-color: #0066cc;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #0055b3;
        }

        button.secondary {
            background-color: #f5f5f7;
            color: #1d1d1f;
            border: 1px solid #d2d2d7;
        }

        button.secondary:hover {
            background-color: #e5e5ea;
        }

        button.primary {
            background-color: #0066cc;
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 8px;
            border: none;
        }

        button.primary:hover {
            background-color: #0055b3;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .form-actions {
            margin: 20px 0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .admin-panel {
            display: none;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #d2d2d7;
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }

        .tab.active {
            border-bottom-color: #0066cc;
            color: #0066cc;
        }

        .tab-content {
            display: none;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .tab-content.active {
            display: block;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: auto;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e5ea;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Điều chỉnh kích thước cột cho bảng adminOrdersTable */
        /* Thêm container cho bảng để có thể cuộn ngang */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 20px;
        }

        #adminOrdersTable {
            min-width: 100%;
            width: auto;
        }

        #adminOrdersTable th, #adminOrdersTable td {
            min-width: 80px; /* Kích thước tối thiểu */
        }

        /* Cột ngày tạo */
        #adminOrdersTable th:nth-child(1), #adminOrdersTable td:nth-child(1) {
            min-width: 100px;
        }

        /* Cột mã đơn */
        #adminOrdersTable th:nth-child(2), #adminOrdersTable td:nth-child(2) {
            min-width: 150px;
        }

        /* Cột khách hàng */
        #adminOrdersTable th:nth-child(3), #adminOrdersTable td:nth-child(3) {
            min-width: 100px;
        }

        /* Cột nhân viên */
        #adminOrdersTable th:nth-child(4), #adminOrdersTable td:nth-child(4) {
            min-width: 100px;
        }

        /* Cột sản phẩm */
        #adminOrdersTable th:nth-child(5), #adminOrdersTable td:nth-child(5) {
            min-width: 200px;
            max-width: 300px;
        }

        /* Cột đã mua */
        #adminOrdersTable th:nth-child(6), #adminOrdersTable td:nth-child(6) {
            min-width: 80px;
        }

        /* Cột giá mua, tỷ giá mua, giá bán, tỷ giá bán */
        #adminOrdersTable th:nth-child(7), #adminOrdersTable td:nth-child(7),
        #adminOrdersTable th:nth-child(8), #adminOrdersTable td:nth-child(8),
        #adminOrdersTable th:nth-child(9), #adminOrdersTable td:nth-child(9),
        #adminOrdersTable th:nth-child(10), #adminOrdersTable td:nth-child(10) {
            min-width: 100px;
        }

        /* Cột đã thanh toán */
        #adminOrdersTable th:nth-child(11), #adminOrdersTable td:nth-child(11) {
            min-width: 150px;
        }

        /* Cột lãi Gross */
        #adminOrdersTable th:nth-child(12), #adminOrdersTable td:nth-child(12) {
            min-width: 120px;
        }

        /* Cột tracking */
        #adminOrdersTable th:nth-child(13), #adminOrdersTable td:nth-child(13) {
            min-width: 150px;
        }

        /* Cột trạng thái đơn, trạng thái thanh toán */
        #adminOrdersTable th:nth-child(14), #adminOrdersTable td:nth-child(14),
        #adminOrdersTable th:nth-child(15), #adminOrdersTable td:nth-child(15) {
            min-width: 120px;
        }

        /* Cột thao tác */
        #adminOrdersTable th:nth-child(16), #adminOrdersTable td:nth-child(16) {
            min-width: 100px;
        }

        /* ===== ORDERS TABLE COLUMN SIZING ===== */
        /* Thêm container cho bảng để có thể cuộn ngang */
        #ordersTable {
            min-width: 100%;
            width: auto;
        }

        #ordersTable th, #ordersTable td {
            min-width: 80px; /* Kích thước tối thiểu */
        }

        /* Cột ngày tạo */
        #ordersTable th:nth-child(1), #ordersTable td:nth-child(1) {
            min-width: 100px;
            text-align: center;
        }

        /* Cột mã đơn */
        #ordersTable th:nth-child(2), #ordersTable td:nth-child(2) {
            min-width: 150px;
        }

        /* Cột khách hàng */
        #ordersTable th:nth-child(3), #ordersTable td:nth-child(3) {
            min-width: 120px;
        }

        /* Cột nhân viên */
        #ordersTable th:nth-child(4), #ordersTable td:nth-child(4) {
            min-width: 100px;
        }

        /* Cột sản phẩm */
        #ordersTable th:nth-child(5), #ordersTable td:nth-child(5) {
            min-width: 200px;
            max-width: 300px;
        }

        /* Cột đã mua */
        #ordersTable th:nth-child(6), #ordersTable td:nth-child(6) {
            min-width: 80px;
        }

        /* Cột tracking */
        #ordersTable th:nth-child(7), #ordersTable td:nth-child(7) {
            min-width: 150px;
        }

        /* Cột status */
        #ordersTable th:nth-child(8), #ordersTable td:nth-child(8) {
            min-width: 120px;
        }

        /* Cột thanh toán */
        #ordersTable th:nth-child(9), #ordersTable td:nth-child(9) {
            min-width: 120px;
        }

        /* Cột thao tác */
        #ordersTable th:nth-child(10), #ordersTable td:nth-child(10) {
            min-width: 100px;
        }

        th {
            background-color: #f5f5f7;
            font-weight: 500;
        }

        tr:hover {
            background-color: #f9f9fb;
        }

        .action-buttons {
            display: flex;
            gap: 6px;
            justify-content: center;
            flex-wrap: nowrap;
        }

        .action-button {
            padding: 8px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .view-button {
            background-color: #0066cc;
            color: white;
        }

        .view-button:hover {
            background-color: #0055b3;
            transform: translateY(-1px);
        }

        .edit-button {
            background-color: #34c759;
            color: white;
        }

        .edit-button:hover {
            background-color: #2da44e;
            transform: translateY(-1px);
        }

        .delete-button {
            background-color: #ff3b30;
            color: white;
        }

        .delete-button:hover {
            background-color: #e6342a;
            transform: translateY(-1px);
        }

        /* Icon styles for action buttons */
        .action-button i {
            font-size: 14px;
            pointer-events: none;
        }

        /* Responsive action buttons */
        @media (max-width: 768px) {
            .action-button {
                min-width: 28px;
                height: 28px;
                padding: 6px;
            }

            .action-button i {
                font-size: 12px;
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 45px;
            padding: 8px 15px;
            border: 1px solid #d2d2d7;
            border-radius: 8px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 45px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 28px;
        }

        .select2-dropdown {
            border: 1px solid #d2d2d7;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .select2-search--dropdown .select2-search__field {
            padding: 8px;
            border: 1px solid #d2d2d7;
            border-radius: 4px;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #0066cc;
        }

        .select2-results__option {
            padding: 8px 12px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ff9500;
            color: white;
        }

        .status-deposited {
            background-color: #007aff;
            color: white;
        }

        .status-completed {
            background-color: #34c759;
            color: white;
        }

        .status-processing {
            background-color: #007aff;
            color: white;
        }

        .status-shipped {
            background-color: #5856d6;
            color: white;
        }

        .text-muted {
            color: #86868b;
            font-style: italic;
        }

        /* Styles for update item modal buttons */
        #setBoughtQuantityBtn:hover {
            background-color: #2da44e !important;
            transform: translateY(-1px);
        }

        #syncPriceBtn:hover {
            background-color: #0055b3 !important;
            transform: translateY(-1px);
        }

        /* Disabled input styling */
        input:disabled {
            background-color: #f5f5f7;
            color: #86868b;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 10100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .loader {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 99999;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .loader-content {
            background-color: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .loader-spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #0066cc;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
        }

        .loader-text {
            font-size: 16px;
            font-weight: 500;
            color: #1d1d1f;
            margin: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Styles for login form */
        .login-type {
            display: flex;
            margin-bottom: 20px;
        }

        .login-type-option {
            flex: 1;
            text-align: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }

        .login-type-option.active {
            border-bottom: 2px solid #0066cc;
            color: #0066cc;
            font-weight: 500;
        }

        .login-logo {
            display: block;
            margin: 0 auto 30px;
            height: 20px;
        }

        /* Styles for purchase order form */
        .purchase-order-form {
            margin-top: 20px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th, .items-table td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #d2d2d7;
        }

        .items-table th {
            background-color: #f5f5f7;
            font-weight: 500;
        }

        .items-table input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d2d2d7;
            border-radius: 6px;
        }

        .remove-row-btn {
            background-color: #ff3b30;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 10px;
            cursor: pointer;
        }

        .error-field {
            border-color: #ff3b30 !important;
        }

        .error-message {
            color: #ff3b30;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .input-error {
            position: relative;
        }

        .input-error::after {
            content: attr(data-error);
            position: absolute;
            left: 0;
            bottom: -18px;
            color: #ff3b30;
            font-size: 12px;
            white-space: nowrap;
        }

        .field-required::after {
            content: " *";
            color: #ff3b30;
        }

        .general-error {
            background-color: #fff0f0;
            border: 1px solid #ff3b30;
            color: #ff3b30;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }

        /* CSS cho nút tạo đơn mua hộ mới */
        .create-invoice-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #0066cc;
            color: white;
            border: none;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 102, 204, 0.3);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .create-invoice-btn:hover {
            background-color: #0055b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 14px rgba(0, 102, 204, 0.4);
        }

        /* CSS cho header của tab tạo đơn mua hộ mới */
        .create-invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        /* CSS cho nút quay lại */
        .back-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background-color: #f5f5f7;
            color: #1d1d1f;
            border: 1px solid #d2d2d7;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background-color: #e5e5ea;
        }

        /* CSS cho phần tìm kiếm và bộ lọc */
        .filter-container {
            background-color: #f9f9fb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 10px;
        }

        .filter-row:last-child {
            margin-bottom: 0;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
            color: #555;
        }

        .filter-group input,
        .filter-group select {
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #d2d2d7;
            font-size: 14px;
        }

        .filter-group button {
            margin-top: auto;
            padding: 10px;
            font-size: 14px;
        }

        .search-results {
            margin-top: 15px;
            font-size: 14px;
            color: #666;
            text-align: right;
        }

        /* CSS cho xem thêm/thu gọn danh sách sản phẩm */
        .product-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-list li {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .product-list-collapsed {
            max-height: 40px;
            overflow: hidden;
            position: relative;
        }

        .product-list-collapsed::after {
            content: "...";
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: white;
            padding-left: 4px;
        }

        .toggle-products {
            color: #0066cc;
            cursor: pointer;
            font-size: 12px;
            margin-top: 5px;
            display: inline-block;
        }

        .toggle-products:hover {
            text-decoration: underline;
        }

        /* Responsive styles */
        /* Dashboard Styles - Theo Figma */
        .dashboard-container {
            background-color: #f6f8fa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            padding: 0;
        }

        .dashboard-main {
            width: 100%;
            padding: 24px;
            overflow-y: auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .dashboard-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #24292e;
            margin: 0;
        }

        .dashboard-date-filter {
            display: flex;
            align-items: center;
            gap: 12px;
            background-color: #ffffff;
            padding: 8px 16px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-range label {
            font-weight: 500;
            white-space: nowrap;
            color: #57606a;
            font-size: 13px;
        }

        .date-range input {
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid #d0d7de;
            background-color: #ffffff;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .date-range input:focus {
            border-color: #0366d6;
            box-shadow: 0 0 0 2px rgba(3, 102, 214, 0.2);
            outline: none;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .dashboard-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .card-title {
            font-size: 13px;
            color: #57606a;
            margin: 0;
            font-weight: 500;
        }

        .card-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 14px;
        }

        .card-icon.blue {
            background-color: #0366d6;
        }

        .card-icon.green {
            background-color: #2da44e;
        }

        .card-icon.orange {
            background-color: #f97316;
        }

        .card-icon.purple {
            background-color: #8250df;
        }

        .card-value {
            font-size: 20px;
            font-weight: 600;
            color: #24292e;
            margin: 0 0 2px 0;
        }

        .card-subtitle {
            font-size: 13px;
            color: #57606a;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .card-subtitle i.up {
            color: #2da44e;
        }

        .card-subtitle i.down {
            color: #cf222e;
        }

        .dashboard-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .dashboard-chart-container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
            height: 320px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .chart-title {
            font-size: 14px;
            font-weight: 600;
            color: #24292e;
            margin: 0;
        }

        .chart-actions {
            display: flex;
            gap: 8px;
        }

        .chart-action {
            padding: 6px 12px;
            border-radius: 6px;
            background-color: #f6f8fa;
            border: 1px solid #d0d7de;
            font-size: 13px;
            color: #57606a;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chart-action:hover {
            background-color: #f0f6fc;
            color: #0366d6;
            border-color: #0366d6;
        }

        .chart-action.active {
            background-color: #0366d6;
            color: #ffffff;
            border-color: #0366d6;
        }

        .dashboard-status-list {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
        }

        .status-header {
            margin-bottom: 10px;
        }

        .status-title {
            font-size: 14px;
            font-weight: 600;
            color: #24292e;
            margin: 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e1e4e8;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #24292e;
        }

        .status-label i {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: block;
        }

        .status-label i.blue {
            background-color: #0366d6;
        }

        .status-label i.green {
            background-color: #2da44e;
        }

        .status-label i.orange {
            background-color: #f97316;
        }

        .status-label i.purple {
            background-color: #8250df;
        }

        .status-label i.red {
            background-color: #cf222e;
        }

        /* Dots cho bảng trạng thái */
        .status-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-dot.orange {
            background-color: #f97316;
        }

        .status-dot.blue {
            background-color: #0366d6;
        }

        .status-dot.purple {
            background-color: #8250df;
        }

        .status-dot.green {
            background-color: #2da44e;
        }

        .status-dot.red {
            background-color: #cf222e;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #24292e;
        }

        .dashboard-table-container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
            margin-bottom: 24px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #24292e;
            margin: 0;
        }

        .dashboard-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 13px;
        }

        .dashboard-table th {
            padding: 8px 12px;
            text-align: left;
            background-color: #f6f8fa;
            color: #57606a;
            font-weight: 500;
            border-bottom: 1px solid #d0d7de;
            white-space: nowrap;
        }

        .dashboard-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e1e4e8;
            color: #24292e;
            white-space: nowrap;
        }

        .dashboard-table tr:last-child td {
            border-bottom: none;
        }

        .dashboard-table tr:hover td {
            background-color: #f6f8fa;
        }

        #staffPerformanceTable {
            width: 100%;
            min-width: 600px;
        }

        .dashboard-alerts {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .alert-group {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid #e1e4e8;
            position: relative;
            overflow: hidden;
        }

        .alert-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: #cf222e;
        }

        .alert-group:nth-child(2)::before {
            background-color: #f97316;
        }

        .alert-group:nth-child(3)::before {
            background-color: #8250df;
        }

        .alert-group:nth-child(4)::before {
            background-color: #0366d6;
        }

        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .alert-title {
            font-size: 14px;
            font-weight: 600;
            color: #24292e;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-title i {
            color: #cf222e;
        }

        .alert-group:nth-child(2) .alert-title i {
            color: #f97316;
        }

        .alert-group:nth-child(3) .alert-title i {
            color: #8250df;
        }

        .alert-group:nth-child(4) .alert-title i {
            color: #0366d6;
        }

        .alert-count {
            font-size: 14px;
            font-weight: 600;
            color: #57606a;
            background-color: #f6f8fa;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .alert-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .alert-item {
            padding: 8px;
            border-radius: 6px;
            background-color: #f6f8fa;
            margin-bottom: 6px;
            font-size: 12px;
            color: #24292e;
            transition: all 0.2s ease;
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-item:hover {
            background-color: #f0f6fc;
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: #f6f8fa;
            border-radius: 4px;
            margin-top: 8px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #0366d6, #2188ff);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        /* Responsive styles */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .dashboard-row {
                grid-template-columns: 1fr;
            }

            .dashboard-alerts {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }

            .dashboard-sidebar {
                width: 100%;
                height: auto;
                position: static;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .form-row {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .items-table {
                display: block;
                overflow-x: auto;
            }

            .filter-row {
                flex-direction: column;
            }

            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .dashboard-cards {
                grid-template-columns: 1fr;
            }

            .dashboard-flex-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Loader -->
    <div class="loader" id="loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <p class="loader-text" id="loaderText">Đang kiểm tra giao dịch...</p>
        </div>
    </div>

    <!-- Login Form (Initially hidden) -->
    <div id="loginContainer" class="login-container" style="display: none;">
        <img src="/images/logo.png" alt="TRUSTMOVE" class="login-logo">
        <h1>Đăng nhập</h1>

        <div class="login-type">
            <div class="login-type-option active" data-type="admin" id="adminLoginOption">Admin</div>
            <div class="login-type-option" data-type="staff" id="staffLoginOption">Nhân viên</div>
        </div>

        <div class="input-group">
            <input type="text" id="username" name="username" placeholder="Tên đăng nhập" required>
            <input type="password" id="password" name="password" placeholder="Mật khẩu" required>
            <button type="button" id="loginBtn">Đăng nhập</button>
        </div>
    </div>

    <!-- Main Content (Initially hidden) -->
    <div id="mainContent" class="admin-panel" style="display: none;">
        <div class="container">
            <div class="header">
                <a href="./admin" title="Trang chủ" style="font-size:24px;color:#0066cc;margin-right:15px;text-decoration:none;"><i class="fas fa-home"></i></a>
                <h1>Quản lý đơn mua hộ</h1>
                <div class="header-actions">
                    <span class="user-name" id="userName">Admin</span>
                    <button class="secondary" id="logoutBtn">Đăng xuất</button>
                </div>
            </div>

            <div class="tabs">
                <div class="tab" data-tab="dashboard" id="dashboardTabButton">Dashboard</div>
                <div class="tab active" data-tab="createOrder">Tạo đơn mua hộ</div>
                <div class="tab" data-tab="manageOrders" id="manageOrdersTabButton">Quản lý đơn</div>

            </div>

            <div class="tab-content active" id="createOrderTab">
                <!-- Create Purchase Order Tab Content -->
                <h3>Danh sách đơn mua hộ</h3>
                <button id="addNewOrderBtn" class="create-invoice-btn" title="Tạo đơn mua hộ mới">
                    <i class="fas fa-plus"></i>
                </button>

                <!-- Phần tìm kiếm và bộ lọc -->
                <div class="filter-container">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="orderSearchInput">Tìm kiếm:</label>
                            <input type="text" id="orderSearchInput" placeholder="Mã đơn hoặc tên khách hàng...">
                        </div>
                        <div class="filter-group">
                            <label for="orderStatusFilter">Trạng thái:</label>
                            <select id="orderStatusFilter">
                                <option value="">Tất cả</option>
                                <option value="pending">Chờ mua</option>
                                <option value="processing">Đang xử lý</option>
                                <option value="shipped">Đã gửi hàng</option>
                                <option value="delivered">Đã giao hàng</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="paymentStatusFilter">Thanh toán:</label>
                            <select id="paymentStatusFilter">
                                <option value="">Tất cả</option>
                                <option value="unpaid">Chưa thanh toán</option>
                                <option value="paid">Đã thanh toán</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="orderStartDate">Từ ngày:</label>
                            <input type="date" id="orderStartDate">
                        </div>
                        <div class="filter-group">
                            <label for="orderEndDate">Đến ngày:</label>
                            <input type="date" id="orderEndDate">
                        </div>
                        <div class="filter-group">
                            <button id="orderResetFilterBtn" class="secondary">Đặt lại bộ lọc</button>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table id="ordersTable">
                        <thead>
                            <tr>
                                <th>Ngày tạo</th>
                                <th>Mã đơn</th>
                                <th>Khách hàng</th>
                                <th>Nhân viên</th>
                                <th>Sản phẩm</th>
                                <th>Đã mua</th>
                                <th>Tracking</th>
                                <th>Status</th>
                                <th>Thanh toán</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Thêm phần hiển thị kết quả tìm kiếm -->
                <div id="orderSearchResults" class="search-results">
                    <span id="orderResultCount"></span>
                </div>
            </div>

            <div class="tab-content" id="createOrderFormTab" style="display: none;">
                <div class="create-invoice-header">
                    <h2>Tạo đơn mua hộ mới</h2>
                    <button id="backToOrderListBtn" class="back-button" title="Quay lại danh sách đơn mua hộ">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </button>
                </div>

                <div id="generalErrorContainer" class="general-error"></div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="userSelect" class="field-required">Chọn khách hàng</label>
                            <select id="userSelect" class="select2"></select>
                            <span id="userSelectError" class="error-message"></span>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="staffSelect">Nhân viên phụ trách</label>
                            <select id="staffSelect" class="select2"></select>
                            <span id="staffSelectError" class="error-message"></span>
                        </div>
                    </div>
                </div>

                <div class="form-actions" style="text-align: right; margin-top: 20px;">
                    <button type="button" id="continueToFormBtn" class="primary">Tiếp tục</button>
                </div>

                <div id="purchaseOrderForm" class="purchase-order-form" style="display: none;">
                    <h4>Yêu cầu mua hàng</h4>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="exchangeRate" class="field-required">Tỷ giá</label>
                                <input type="number" id="exchangeRate" name="exchangeRate" step="0.01" required>
                                <span id="exchangeRateError" class="error-message"></span>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="depositPercentage" class="field-required">Tỷ lệ cọc (%)</label>
                                <input type="number" id="depositPercentage" name="depositPercentage" min="0" max="100" required>
                                <span id="depositPercentageError" class="error-message"></span>
                            </div>
                        </div>
                    </div>

                    <table class="items-table" id="itemsTable">
                        <thead>
                            <tr>
                                <th>Link sản phẩm</th>
                                <th>Tên sản phẩm <span class="field-required"></span></th>
                                <th>Phân loại</th>
                                <th>Số lượng <span class="field-required"></span></th>
                                <th>Giá bán($) cho khách <span class="field-required"></span></th>
                                <th>Xóa</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- Items will be added here dynamically -->
                        </tbody>
                    </table>
                    <div id="itemsErrorContainer" class="error-message" style="margin-top: 10px;"></div>

                    <div class="form-group">
                        <label for="note">Ghi chú</label>
                        <textarea id="note" name="note" rows="3"></textarea>
                    </div>

                    <div style="text-align: right;">
                        <button type="button" id="createOrderBtn">Tiếp tục</button>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="dashboardTab">
                <!-- Dashboard Tab Content -->
                <div class="dashboard-container">
                    <div class="dashboard-main">
                        <div class="dashboard-header">
                            <h3>Dashboard</h3>
                            <div class="dashboard-date-filter">
                                <div class="date-range">
                                    <label for="dashboardStartDate"><i class="far fa-calendar-alt"></i> Từ:</label>
                                    <input type="date" id="dashboardStartDate">
                                </div>
                                <div class="date-range">
                                    <label for="dashboardEndDate"><i class="far fa-calendar-alt"></i> Đến:</label>
                                    <input type="date" id="dashboardEndDate">
                                </div>
                                <button id="applyDateFilter" class="chart-action active"><i class="fas fa-filter"></i></button>
                                <button id="resetDateFilter" class="chart-action"><i class="fas fa-redo-alt"></i></button>
                            </div>
                        </div>
                        <!-- Thêm phần tử hiển thị lỗi -->
                        <div id="dashboardErrorMessage" class="error-message" style="margin-bottom: 15px; color: #ff3b30; display: none;"></div>

                    <!-- Tổng quan tài chính & hoạt động -->
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Đơn hàng</h5>
                                <div class="card-icon blue"><i class="fas fa-shopping-cart"></i></div>
                            </div>
                            <div class="card-value" id="totalActiveOrders">0</div>
                            <div class="card-subtitle">
                                <!-- Phần trăm tăng/giảm sẽ được cập nhật từ dữ liệu thực tế -->
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Doanh Thu</h5>
                                <div class="card-icon green"><i class="fas fa-dollar-sign"></i></div>
                            </div>
                            <div class="card-value" id="totalSellVND">0 VND</div>
                            <div class="card-subtitle" id="totalSellUSD">$0</div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Chi Phí</h5>
                                <div class="card-icon orange"><i class="fas fa-shopping-bag"></i></div>
                            </div>
                            <div class="card-value" id="totalPurchaseVND">0 VND</div>
                            <div class="card-subtitle" id="totalPurchaseUSD">$0</div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5 class="card-title">Lợi Nhuận</h5>
                                <div class="card-icon purple"><i class="fas fa-chart-line"></i></div>
                            </div>
                            <div class="card-value" id="totalGrossProfit">0 VND</div>
                            <div class="card-subtitle" id="profitMargin">0%</div>
                        </div>
                    </div>

                    <div class="dashboard-row">
                        <div class="dashboard-chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">Tiến độ mua hàng</h5>
                                <div class="chart-actions">
                                    <div class="chart-action active">Ngày</div>
                                    <div class="chart-action">Tuần</div>
                                    <div class="chart-action">Tháng</div>
                                </div>
                            </div>
                            <div>
                                <div id="totalItemsToBuy">Cần mua: 0</div>
                                <div id="totalItemsBought">Đã mua: 0</div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar" id="purchaseProgressBar" style="width: 0%;"></div>
                                </div>
                            </div>
                            <div style="position: relative; height: 180px; width: 100%; overflow: hidden;">
                                <canvas id="purchaseProgressChart"></canvas>
                            </div>
                        </div>
                        <div class="dashboard-status-list">
                            <div class="status-header">
                                <h5 class="status-title">Thanh toán</h5>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="blue"></i>
                                    <span>Đã cọc</span>
                                </div>
                                <div class="status-value" id="totalDeposited">0 VND</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="green"></i>
                                    <span>Đã TT đủ</span>
                                </div>
                                <div class="status-value" id="totalPaid">0 VND</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="purple"></i>
                                    <span>Đã thu</span>
                                </div>
                                <div class="status-value" id="totalCollected">0 VND</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="red"></i>
                                    <span>Còn nợ</span>
                                </div>
                                <div class="status-value" id="totalDebt">0 VND</div>
                            </div>
                        </div>
                    </div>

                    <!-- Trạng thái đơn hàng -->
                    <div class="dashboard-row">
                        <div class="dashboard-chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">Trạng thái đơn</h5>
                                <div class="chart-actions">
                                    <div class="chart-action active">Biểu đồ</div>
                                    <div class="chart-action">Danh sách</div>
                                </div>
                            </div>
                            <div style="position: relative; height: 200px; max-width: 100%;">
                                <canvas id="orderStatusChart"></canvas>
                            </div>
                        </div>
                        <div class="dashboard-status-list">
                            <div class="status-header">
                                <h5 class="status-title">Chi tiết</h5>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="orange"></i>
                                    <span>Đơn mới</span>
                                </div>
                                <div class="status-value" id="newOrdersCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="blue"></i>
                                    <span>Đã cọc</span>
                                </div>
                                <div class="status-value" id="depositedOrdersCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="purple"></i>
                                    <span>Đang mua</span>
                                </div>
                                <div class="status-value" id="buyingOrdersCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="green"></i>
                                    <span>Hoàn thành</span>
                                </div>
                                <div class="status-value" id="completedOrdersCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="red"></i>
                                    <span>Đã hủy</span>
                                </div>
                                <div class="status-value" id="cancelledOrdersCount">0</div>
                            </div>
                        </div>
                    </div>



                    <!-- Trạng thái thanh toán -->
                    <div class="dashboard-row">
                        <div class="dashboard-chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">Thanh toán</h5>
                                <div class="chart-actions">
                                    <div class="chart-action active">Biểu đồ</div>
                                    <div class="chart-action">Danh sách</div>
                                </div>
                            </div>
                            <div style="position: relative; height: 200px; max-width: 100%;">
                                <canvas id="paymentStatusChart"></canvas>
                            </div>
                        </div>
                        <div class="dashboard-status-list">
                            <div class="status-header">
                                <h5 class="status-title">Chi tiết</h5>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="orange"></i>
                                    <span>Chưa TT</span>
                                </div>
                                <div class="status-value" id="unpaidOrdersCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="blue"></i>
                                    <span>Đã cọc</span>
                                </div>
                                <div class="status-value" id="depositedPaymentCount">0</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">
                                    <i class="green"></i>
                                    <span>Đã TT đủ</span>
                                </div>
                                <div class="status-value" id="paidOrdersCount">0</div>
                            </div>
                        </div>
                    </div>

                    <!-- Hiệu suất nhân viên -->
                    <div class="dashboard-row">
                        <div class="dashboard-chart-container" style="grid-column: span 2;">
                            <div class="chart-header">
                                <h5 class="chart-title">Hiệu suất nhân viên</h5>
                            </div>
                            <div style="overflow-x: auto; height: calc(100% - 40px);">
                                <table class="dashboard-table" id="staffPerformanceTable">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-user"></i> Nhân viên</th>
                                            <th><i class="fas fa-shopping-cart"></i> Tổng đơn</th>
                                            <th><i class="fas fa-check-circle"></i> Hoàn thành</th>
                                            <th><i class="fas fa-spinner"></i> Đang xử lý</th>
                                            <th><i class="fas fa-chart-line"></i> Lợi Nhuận Gộp</th>
                                            <th><i class="fas fa-percentage"></i> Tỷ lệ đúng hạn</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>



                    <!-- Cảnh báo -->
                    <div class="dashboard-alerts">
                        <div class="alert-group">
                            <div class="alert-header">
                                <h5 class="alert-title"><i class="fas fa-exclamation-triangle"></i> Lợi nhuận âm</h5>
                                <div class="alert-count" id="negativeGrossProfitCount">0</div>
                            </div>
                            <div class="alert-list" id="negativeGrossProfitList">
                                <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                            </div>
                        </div>
                        <div class="alert-group">
                            <div class="alert-header">
                                <h5 class="alert-title"><i class="fas fa-clock"></i> Chờ mua >2 ngày</h5>
                                <div class="alert-count" id="waitingToBuyCount">0</div>
                            </div>
                            <div class="alert-list" id="waitingToBuyList">
                                <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                            </div>
                        </div>
                        <div class="alert-group">
                            <div class="alert-header">
                                <h5 class="alert-title"><i class="fas fa-truck"></i> Tracking chưa cập nhật</h5>
                                <div class="alert-count" id="trackingNotUpdatedCount">0</div>
                            </div>
                            <div class="alert-list" id="trackingNotUpdatedList">
                                <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                            </div>
                        </div>
                        <div class="alert-group">
                            <div class="alert-header">
                                <h5 class="alert-title"><i class="fas fa-money-bill-wave"></i> Quá hạn thanh toán</h5>
                                <div class="alert-count" id="overduePaymentCount">0</div>
                            </div>
                            <div class="alert-list" id="overduePaymentList">
                                <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <div class="tab-content" id="manageOrdersTab">
                <!-- Manage Orders Tab Content -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0;">Quản lý đơn mua hộ</h3>

                    <div class="form-group" style="margin: 0; width: 300px;">
                        <input type="text" id="manageOrderSearchInput" placeholder="Tìm kiếm theo mã đơn hoặc tên khách hàng..." style="width: 100%; padding: 8px 12px; font-size: 14px;">
                    </div>
                </div>

                <!-- Bảng thông tin chi tiết cho admin -->
                <div id="adminDetailSection">
                    <!-- Table Controls -->
                    <div class="admin-table-controls">
                        <div class="table-controls-left">
                            <div class="table-info">
                                <span>Bảng quản lý đơn hàng</span>
                            </div>
                        </div>
                        <div class="table-controls-right">
                            <button id="exportExcelBtn" class="table-control-btn" title="Xuất dữ liệu ra Excel">
                                <i class="fas fa-file-excel"></i>
                                <span>Xuất Excel</span>
                            </button>
                            <button id="showAllColumnsBtn" class="table-control-btn" title="Hiển thị tất cả cột">
                                <i class="fas fa-eye"></i>
                                <span>Hiện tất cả</span>
                            </button>
                            <button id="fullscreenBtn" class="table-control-btn" title="Chế độ toàn màn hình">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <style>
                        /* Admin Table Controls */
                        .admin-table-controls {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 15px;
                            padding: 10px 15px;
                            background-color: #f8f9fa;
                            border-radius: 8px;
                            border: 1px solid #e9ecef;
                        }

                        .table-controls-left,
                        .table-controls-right {
                            display: flex;
                            gap: 10px;
                            align-items: center;
                        }

                        .table-info span {
                            font-weight: 600;
                            color: #24292e;
                            font-size: 14px;
                        }

                        .table-control-btn {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            padding: 8px 12px;
                            background-color: #ffffff;
                            border: 1px solid #d0d7de;
                            border-radius: 6px;
                            font-size: 13px;
                            color: #24292e;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            position: relative;
                        }

                        .table-control-btn:hover {
                            background-color: #f6f8fa;
                            border-color: #0366d6;
                            color: #0366d6;
                        }

                        /* Excel export button styling */
                        #exportExcelBtn {
                            background-color: #2da44e;
                            color: white;
                            border-color: #2da44e;
                        }

                        #exportExcelBtn:hover {
                            background-color: #2c974b;
                            color: white;
                            border-color: #2c974b;
                        }

                        .table-control-btn i {
                            font-size: 14px;
                        }

                        /* Column Visibility Menu */
                        .column-visibility-menu {
                            position: absolute;
                            top: 100%;
                            left: 0;
                            z-index: 10200;
                            background-color: #ffffff;
                            border: 1px solid #d0d7de;
                            border-radius: 6px;
                            box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
                            min-width: 200px;
                            margin-top: 4px;
                        }

                        .menu-header {
                            padding: 12px 16px;
                            border-bottom: 1px solid #e1e4e8;
                            font-weight: 600;
                            font-size: 14px;
                            color: #24292e;
                            background-color: #f6f8fa;
                            border-radius: 6px 6px 0 0;
                        }

                        .menu-content {
                            padding: 8px 0;
                            max-height: 300px;
                            overflow-y: auto;
                        }

                        .column-checkbox-item {
                            display: flex;
                            align-items: center;
                            padding: 8px 16px;
                            cursor: pointer;
                            transition: background-color 0.2s ease;
                        }

                        .column-checkbox-item:hover {
                            background-color: #f6f8fa;
                        }

                        .column-checkbox-item input[type="checkbox"] {
                            margin-right: 8px;
                            cursor: pointer;
                        }

                        .column-checkbox-item label {
                            cursor: pointer;
                            font-size: 13px;
                            color: #24292e;
                            margin: 0;
                        }

                        /* Fullscreen Mode */
                        .admin-table-fullscreen {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100vw;
                            height: 100vh;
                            background-color: #ffffff;
                            z-index: 9999;
                            padding: 20px;
                            overflow: auto;
                        }

                        .admin-table-fullscreen .admin-table-controls {
                            margin-bottom: 20px;
                        }

                        .admin-table-fullscreen #fullscreenBtn i {
                            transform: rotate(45deg);
                        }

                        /* Ensure action buttons are clickable in fullscreen mode */
                        .admin-table-fullscreen .action-button {
                            position: relative;
                            z-index: 10001;
                            pointer-events: auto;
                        }

                        .admin-table-fullscreen .action-buttons {
                            position: relative;
                            z-index: 10001;
                        }

                        /* Ensure Select2 dropdowns appear above fullscreen layer */
                        .select2-container--open .select2-dropdown {
                            z-index: 10300 !important;
                        }

                        .select2-container--open .select2-dropdown--above {
                            z-index: 10300 !important;
                        }

                        .select2-container--open .select2-dropdown--below {
                            z-index: 10300 !important;
                        }

                        /* Column Resize Handles */
                        .column-resizer {
                            position: absolute;
                            top: 0;
                            right: -2px;
                            width: 6px;
                            height: 100%;
                            background-color: transparent;
                            cursor: col-resize;
                            z-index: 15;
                            border-right: 2px solid transparent;
                        }

                        .column-resizer:hover,
                        .column-resizer.resizing {
                            border-right: 2px solid #0366d6;
                            background-color: rgba(3, 102, 214, 0.1);
                        }

                        #adminOrdersTable th {
                            position: relative;
                            border-right: 1px solid #e5e5ea;
                            min-width: 50px;
                            padding-right: 35px !important;
                        }

                        /* Ensure all columns are resizable */
                        #adminOrdersTable th:not(:last-child) {
                            resize: horizontal;
                            overflow: hidden;
                        }

                        /* ===== ORDERS TABLE RESIZING STYLES ===== */
                        #ordersTable th {
                            position: relative;
                            border-right: 1px solid #e5e5ea;
                            min-width: 50px;
                            padding-right: 35px !important;
                        }

                        /* Ensure all columns are resizable except last one */
                        #ordersTable th:not(:last-child) {
                            resize: horizontal;
                            overflow: hidden;
                        }

                        /* Column resizer for ordersTable */
                        #ordersTable .column-resizer {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            width: 5px;
                            cursor: col-resize;
                            background-color: transparent;
                            border-right: 1px solid transparent;
                            z-index: 10;
                            transition: all 0.2s ease;
                        }

                        #ordersTable .column-resizer:hover,
                        #ordersTable .column-resizer.resizing {
                            border-right: 2px solid #0366d6;
                            background-color: rgba(3, 102, 214, 0.1);
                        }

                        /* Column visibility icons */
                        .column-visibility-icon {
                            position: absolute;
                            top: 50%;
                            right: 8px;
                            transform: translateY(-50%);
                            width: 16px;
                            height: 16px;
                            cursor: pointer;
                            color: #6c757d;
                            font-size: 12px;
                            z-index: 20;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 3px;
                            transition: all 0.2s ease;
                        }

                        .column-visibility-icon:hover {
                            background-color: #f6f8fa;
                            color: #0366d6;
                        }

                        .column-visibility-icon.hidden {
                            color: #dc3545;
                        }

                        /* Hide visibility icon for action column */
                        #adminOrdersTable th:last-child .column-visibility-icon {
                            display: none;
                        }

                        /* Loading indicator for table operations */
                        .table-loading {
                            position: relative;
                        }

                        .table-loading::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background-color: rgba(255, 255, 255, 0.8);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 100;
                        }

                        /* CSS để giảm kích thước chữ và padding của bảng admin */
                        #adminOrdersTable {
                            font-size: 13px;
                            border-collapse: collapse;
                            width: 100%;
                        }

                        #adminOrdersTable th,
                        #adminOrdersTable td {
                            padding: 8px 6px;
                            white-space: nowrap;
                            border: 1px solid #e5e5ea;
                        }

                        #adminOrdersTable th {
                            background-color: #f5f5f7;
                            font-weight: 500;
                            font-size: 12px;
                            text-align: center;
                            position: sticky;
                            top: 0;
                            z-index: 10;
                        }

                        #adminOrdersTable .product-list {
                            font-size: 12px;
                            max-width: 150px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        #adminOrdersTable .product-list li {
                            margin-bottom: 2px;
                            line-height: 1.2;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        #adminOrdersTable .product-list-collapsed {
                            max-height: 20px;
                        }

                        #adminOrdersTable .toggle-products {
                            font-size: 11px;
                            margin-top: 2px;
                        }

                        #adminOrdersTable .status-badge {
                            font-size: 11px;
                            padding: 2px 5px;
                        }

                        #adminOrdersTable .action-button {
                            padding: 4px 8px;
                            font-size: 12px;
                        }

                        /* Thêm thanh cuộn ngang cho bảng nếu cần */
                        #adminDetailSection {
                            overflow-x: auto;
                        }

                        /* Điều chỉnh độ rộng cột */
                        #adminOrdersTable th:nth-child(1), /* Ngày tạo */
                        #adminOrdersTable td:nth-child(1) {
                            max-width: 100px;
                            text-align: center;
                        }

                        #adminOrdersTable th:nth-child(2), /* Mã đơn */
                        #adminOrdersTable td:nth-child(2) {
                            max-width: 120px;
                        }

                        #adminOrdersTable th:nth-child(3), /* Khách hàng */
                        #adminOrdersTable td:nth-child(3) {
                            max-width: 80px;
                        }

                        #adminOrdersTable th:nth-child(4), /* Nhân viên */
                        #adminOrdersTable td:nth-child(4) {
                            width: auto;
                            min-width: 50px;
                            max-width: fit-content;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        #adminOrdersTable th:nth-child(5), /* Sản phẩm */
                        #adminOrdersTable td:nth-child(5) {
                            max-width: 150px;
                        }

                        #adminOrdersTable th:nth-child(6), /* Đã mua */
                        #adminOrdersTable td:nth-child(6),
                        #adminOrdersTable th:nth-child(7), /* Giá mua */
                        #adminOrdersTable td:nth-child(7),
                        #adminOrdersTable th:nth-child(8), /* Tỷ giá mua */
                        #adminOrdersTable td:nth-child(8),
                        #adminOrdersTable th:nth-child(9), /* Giá bán($) */
                        #adminOrdersTable td:nth-child(9),
                        #adminOrdersTable th:nth-child(10), /* Tỷ Giá bán*/
                        #adminOrdersTable td:nth-child(10) {
                            max-width: 70px;
                            text-align: right;
                        }

                        #adminOrdersTable th:nth-child(11), /* Đã thanh toán */
                        #adminOrdersTable td:nth-child(11),
                        #adminOrdersTable th:nth-child(12), /* Lãi Gross */
                        #adminOrdersTable td:nth-child(12) {
                            max-width: 100px;
                            text-align: right;
                        }

                        #adminOrdersTable th:nth-child(13), /* Tracking */
                        #adminOrdersTable td:nth-child(13) {
                            max-width: 120px;
                            white-space: pre-line;
                            line-height: 1.4;
                        }

                        #adminOrdersTable td:nth-child(13) .tracking-numbers {
                            font-size: 11px;
                            line-height: 1.3;
                            display: flex;
                            flex-direction: column;
                            gap: 3px;
                        }

                        #adminOrdersTable td:nth-child(13) .tracking-number {
                            display: inline-block;
                            padding: 2px 6px;
                            background-color: #f0f6fc;
                            border-radius: 4px;
                            border: 1px solid #d0d7de;
                            font-family: 'Courier New', monospace;
                            font-size: 10px;
                            color: #0969da;
                            white-space: nowrap;
                            max-width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        #adminOrdersTable td:nth-child(13) .tracking-number:hover {
                            background-color: #ddf4ff;
                            border-color: #0969da;
                        }

                        /* Tracking numbers styling for ordersTable (staff/admin view) */
                        #ordersTable td:nth-child(7) .tracking-numbers {
                            font-size: 11px;
                            line-height: 1.3;
                            display: flex;
                            flex-direction: column;
                            gap: 3px;
                        }

                        #ordersTable td:nth-child(7) .tracking-number {
                            display: inline-block;
                            padding: 2px 6px;
                            background-color: #f0f6fc;
                            border-radius: 4px;
                            border: 1px solid #d0d7de;
                            font-family: 'Courier New', monospace;
                            font-size: 10px;
                            color: #0969da;
                            white-space: nowrap;
                            max-width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        #ordersTable td:nth-child(7) .tracking-number:hover {
                            background-color: #ddf4ff;
                            border-color: #0969da;
                        }

                        #adminOrdersTable th:nth-child(14), /* Trạng thái đơn */
                        #adminOrdersTable td:nth-child(14) {
                            max-width: 80px;
                            text-align: center;
                        }

                        #adminOrdersTable th:nth-child(15), /* Trạng thái thanh toán */
                        #adminOrdersTable td:nth-child(15) {
                            max-width: 80px;
                            text-align: center;
                        }

                        #adminOrdersTable th:nth-child(16), /* Thao tác */
                        #adminOrdersTable td:nth-child(16) {
                            max-width: 80px;
                            text-align: center;
                        }
                    </style>
                    <div class="table-container">
                        <table id="adminOrdersTable">
                            <thead>
                                <tr>
                                    <th>Ngày tạo</th>
                                    <th>Mã đơn</th>
                                    <th>Khách hàng</th>
                                    <th>Nhân viên</th>
                                    <th>Sản phẩm</th>
                                    <th>Đã mua</th>
                                    <th>Giá mua($)</th>
                                    <th>Tỷ giá mua</th>
                                    <th>Giá bán($)</th>
                                    <th>Tỷ Giá bán</th>
                                    <th>Đã thanh toán</th>
                                    <th title="Lãi Gross = (Giá bán × Số lượng đã mua × Tỷ giá bán) - (Giá mua × Số lượng đã mua × Tỷ giá mua). Hover vào giá trị để xem chi tiết tính toán." style="cursor: help;">Lãi Gross ℹ️</th>
                                    <th>Tracking</th>
                                    <th>Trạng thái đơn</th>
                                    <th>Trạng thái thanh toán</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody id="adminOrdersTableBody">
                                <!-- Admin orders will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Detail Modal -->
    <div id="orderDetailModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="orderDetailContent"></div>
            <div class="modal-actions">
                <button id="addTrackingBtn">Thêm tracking</button>
                <button id="checkPaymentBtn" class="secondary">Kiểm tra thanh toán</button>
                <button id="manualPaymentBtn" class="secondary">Xác nhận thanh toán thủ công</button>
            </div>
        </div>
    </div>

    <!-- Add Tracking Modal -->
    <div id="addTrackingModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Thêm tracking</h3>
            <div class="form-group">
                <label for="trackingItemSelect">Chọn sản phẩm</label>
                <select id="trackingItemSelect"></select>
            </div>
            <div class="form-group">
                <label for="trackingNumber">Tracking number</label>
                <input type="text" id="trackingNumber" required>
            </div>
            <div class="form-group">
                <label for="warehouseSelect">Kho</label>
                <select id="warehouseSelect"></select>
            </div>
            <div class="modal-actions">
                <button id="saveTrackingBtn">Lưu</button>
                <button class="secondary close-modal">Hủy</button>
            </div>
        </div>
    </div>

    <!-- Update Item Modal -->
    <div id="updateItemModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Cập nhật thông tin sản phẩm</h3>
            <div class="form-group">
                <label for="purchasedQuantity">Số lượng đã mua</label>
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px;">
                    <input type="number" id="purchasedQuantity" min="0" required style="flex: 1;">
                    <span id="requiredQuantityDisplay" style="color: #666; font-size: 14px; white-space: nowrap;">Cần mua: 0</span>
                    <button type="button" id="setBoughtQuantityBtn" style="padding: 8px 12px; background-color: #34c759; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">Đã mua</button>
                </div>
                <small style="color: #666; font-style: italic;">Nhập số lượng đã mua hoặc click "Đã mua" để điền tự động</small>
            </div>
            <div class="form-group">
                <label>Giá mua</label>
                <div style="display: flex; gap: 10px; margin-bottom: 5px;">
                    <div style="flex: 1;">
                        <label for="purchasePriceUSD" style="font-size: 12px; color: #666; margin-bottom: 3px; display: block;">Giá mua ngoại tệ (USD)</label>
                        <input type="number" id="purchasePriceUSD" step="0.01" min="0" required style="width: 100%;">
                    </div>
                    <div style="flex: 1;">
                        <label for="purchasePriceVND" style="font-size: 12px; color: #666; margin-bottom: 3px; display: block;">Giá mua VNĐ</label>
                        <input type="number" id="purchasePriceVND" step="1" min="0" required style="width: 100%;">
                    </div>
                    <button type="button" id="syncPriceBtn" title="Điền giá mua = giá bán. Trong chế độ tỷ giá thủ công, chỉ thay đổi giá mua mà không động vào tỷ giá." style="padding: 15px 12px; background-color: #007aff; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer; white-space: nowrap; align-self: end;">Đồng bộ</button>
                </div>
                <small style="color: #666; font-style: italic;">Nhập vào 1 trong 2 ô sẽ tự động tính giá trị cho ô còn lại. Click "Đồng bộ" để điền giá mua = giá bán</small>
            </div>
            <div class="form-group">
                <label for="purchaseExchangeRate">Tỷ giá mua</label>
                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                    <div style="flex: 1; position: relative;">
                        <input type="number" id="purchaseExchangeRate" step="0.01" min="0" required style="width: 100%;" disabled>
                        <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); display: flex; align-items: center; background: white; padding: 0 5px;">
                            <input type="checkbox" id="manualRateInput" style="margin-right: 5px;">
                            <label for="manualRateInput" style="font-size: 12px; margin: 0; white-space: nowrap;">Nhập thủ công</label>
                        </div>
                    </div>
                </div>
                <small style="color: #666; font-style: italic;">Tỷ giá được tính tự động từ giá mua. Tick "Nhập thủ công" để chỉnh sửa</small>
                <div id="manualRateWarning" style="display: none; margin-top: 8px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; font-size: 12px; color: #856404;">
                    <strong>⚠️ Chế độ nhập thủ công:</strong> Khi bạn nhập tỷ giá thủ công, hệ thống sẽ KHÔNG tự động thay đổi giá mua. Hãy đảm bảo giá mua và tỷ giá phù hợp để có lãi như mong muốn.
                </div>
            </div>
            <div class="form-group">
                <label for="currencySelect">Đơn vị tiền tệ</label>
                <select id="currencySelect" style="width: 100%;">
                    <option value="USD" data-rate="" selected>USD - Mặc định</option>
                    <!-- Các tùy chọn khác sẽ được thêm bằng JavaScript -->
                </select>
                <small style="color: #666; font-style: italic;">Chọn đơn vị tiền tệ để tính toán tỷ giá mua</small>
            </div>
            <div id="grossProfitDisplay" style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 6px; font-size: 14px; font-weight: 600; text-align: center; border: 2px solid #e9ecef;">
                Lãi Gross: Chưa tính toán
            </div>
            <div style="margin-bottom: 20px; padding: 10px; background-color: #f5f5f7; border-radius: 6px; font-size: 13px;">
                <p style="margin: 0 0 5px 0;"><strong>Tính toán tự động:</strong></p>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Khi nhập giá mua USD: Giá mua VNĐ = Giá mua USD × Tỷ giá USD</li>
                    <li>Khi nhập giá mua VNĐ: Giá mua USD = Giá mua VNĐ ÷ Tỷ giá USD</li>
                    <li>Tỷ giá mua = (Giá mua USD ÷ Giá bán USD) × Tỷ giá đơn vị tiền tệ đã chọn</li>
                    <li><strong>Lãi Gross = (Giá bán × Số lượng × Tỷ giá bán) - (Giá mua × Số lượng đã mua × Tỷ giá mua)</strong></li>
                    <li>Nút "Đồng bộ": Điền giá mua USD = giá bán USD (trong chế độ tỷ giá thủ công, chỉ thay đổi giá mà không động vào tỷ giá)</li>
                    <li>Nút "Đã mua": Điền số lượng đã mua = số lượng cần mua</li>
                </ul>
            </div>
            <div class="modal-actions">
                <button id="saveItemUpdateBtn">Lưu</button>
                <button class="secondary close-modal">Hủy</button>
            </div>
        </div>
    </div>

    <!-- Manual Payment Modal -->
    <div id="manualPaymentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Xác nhận thanh toán thủ công</h3>
            <div class="form-group">
                <label for="manualPaymentAmount">Số tiền đã nhận <span style="color: red;">*</span></label>
                <input type="number" id="manualPaymentAmount" step="0.01" min="0" required placeholder="Nhập số tiền đã nhận">
                <small style="color: #666; font-style: italic;">Nhập số tiền thực tế đã nhận từ khách hàng</small>
            </div>
            <div class="form-group">
                <label for="manualPaymentDate">Ngày nhận tiền</label>
                <input type="date" id="manualPaymentDate" required>
                <small style="color: #666; font-style: italic;">Ngày thực tế nhận được tiền</small>
            </div>
            <div class="form-group">
                <label for="manualPaymentNote">Ghi chú (tùy chọn)</label>
                <textarea id="manualPaymentNote" rows="3" placeholder="Nhập ghi chú về việc thanh toán (ví dụ: phương thức thanh toán, người nhận tiền, v.v.)"></textarea>
                <small style="color: #666; font-style: italic;">Thông tin bổ sung về việc thanh toán</small>
            </div>
            <div style="margin-bottom: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                <p style="margin: 0 0 10px 0; font-weight: bold; color: #856404;">⚠️ Lưu ý quan trọng:</p>
                <ul style="margin: 0; padding-left: 20px; color: #856404;">
                    <li>Chức năng này dành cho việc xác nhận thanh toán thủ công khi không thể kiểm tra tự động qua TPBank</li>
                    <li>Vui lòng đảm bảo số tiền và thông tin chính xác trước khi xác nhận</li>
                    <li>Thông tin này sẽ được lưu vào hệ thống và không thể hoàn tác</li>
                </ul>
            </div>
            <div class="modal-actions">
                <button id="confirmManualPaymentBtn" style="background-color: #28a745; color: white;">Xác nhận thanh toán</button>
                <button class="secondary close-modal">Hủy</button>
            </div>
        </div>
    </div>

    <!-- Edit Order Modal -->
    <div id="editOrderModal" class="modal">
        <div class="modal-content" style="max-width: 1000px; max-height: 90vh; overflow-y: auto;">
            <span class="close">&times;</span>
            <h3>Chỉnh sửa đơn mua hộ</h3>

            <div class="form-row">
                <div class="form-col">
                    <div class="form-group">
                        <label for="editUserSelect">Khách hàng *</label>
                        <select id="editUserSelect" required>
                            <option value="">Chọn khách hàng</option>
                        </select>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-group">
                        <label for="editStaffSelect">Nhân viên phụ trách *</label>
                        <select id="editStaffSelect" required>
                            <option value="">Chọn nhân viên</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-col">
                    <div class="form-group">
                        <label for="editExchangeRate">Tỷ giá bán (VND/$) *</label>
                        <input type="number" id="editExchangeRate" step="1" min="1" required>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-group">
                        <label for="editDepositPercentage">Phần trăm cọc (%) *</label>
                        <input type="number" id="editDepositPercentage" step="1" min="0" max="100" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="editNote">Ghi chú</label>
                <textarea id="editNote" rows="3" placeholder="Nhập ghi chú cho đơn hàng..."></textarea>
            </div>

            <div class="form-group">
                <label>Danh sách sản phẩm *</label>
                <table class="items-table" id="editItemsTable">
                    <thead>
                        <tr>
                            <th>Link sản phẩm</th>
                            <th>Tên sản phẩm *</th>
                            <th>Phân loại</th>
                            <th>Số lượng *</th>
                            <th>Giá bán ($) *</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="editItemsTableBody">
                        <!-- Items will be loaded here -->
                    </tbody>
                </table>
                <button type="button" id="addEditItemBtn" class="secondary">Thêm sản phẩm</button>
            </div>

            <div class="modal-actions">
                <button id="saveEditOrderBtn" class="primary">Lưu thay đổi</button>
                <button type="button" class="secondary" onclick="document.getElementById('editOrderModal').style.display='none'">Hủy</button>
            </div>
        </div>
    </div>

    <script>
        // Biến toàn cục
        let API_URL;
        let isAdmin = false;
        let currentOrderId = null;
        let currentItemId = null;
        let allUsers = [];
        let allStaff = []; // Lưu trữ danh sách nhân viên
        let currentStaffId = null; // Lưu trữ ID nhân viên đang đăng nhập
        let allWarehouses = [];
        let purchaseOrderItems = [];
        let currentItem = null; // Lưu trữ thông tin sản phẩm hiện tại đang cập nhật
        let usdExchangeRate = null; // Lưu trữ tỷ giá USD từ API
        let allExchangeRates = []; // Lưu trữ tất cả tỷ giá từ API
        let customExchangeRates = []; // Lưu trữ tỷ giá tùy chỉnh từ API
        let editOrderItems = []; // Lưu trữ danh sách sản phẩm đang chỉnh sửa
        let currentEditOrderId = null; // Lưu trữ ID đơn hàng đang chỉnh sửa

        // Biến cho Dashboard
        let dashboardData = {
            startDate: null,
            endDate: null,
            orders: [],
            orderStatusChart: null,
            paymentStatusChart: null
        };

        // Định dạng tiền tệ
        const formatter = new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        });

        // Hàm hiển thị/ẩn loader
        function showLoader(show = true, text = 'Đang xử lý...') {
            const loader = document.getElementById('loader');
            const loaderText = document.getElementById('loaderText');

            if (show) {
                loaderText.textContent = text;
                loader.style.display = 'flex';
            } else {
                loader.style.display = 'none';
            }
        }

        // Khởi tạo khi trang tải
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Tải cấu hình
                const response = await fetch('/config.json');
                if (response.ok) {
                    const config = await response.json();
                    API_URL = config.apiUrl || '';
                    console.log('Đã tải cấu hình API URL:', API_URL);
                } else {
                    // Fallback khi không tải được cấu hình
                    API_URL = location.hostname === 'localhost' || location.hostname === '127.0.0.1'
                        ? `${location.protocol}//${location.hostname}:3000`
                        : `${location.protocol}//${location.hostname}`;
                    console.log('Sử dụng cấu hình API URL mặc định:', API_URL);
                }

                // Khởi tạo Select2
                $('.select2').select2({
                    width: '100%',
                    placeholder: 'Chọn...'
                });

                // Kiểm tra phiên đăng nhập
                const token = localStorage.getItem('admin_token') || localStorage.getItem('staff_token');
                if (token) {
                    await checkSession(token);
                } else {
                    showLoginForm();
                }

                // Thiết lập sự kiện cho các tab
                setupTabEvents();

                // Thiết lập sự kiện cho form đăng nhập
                setupLoginEvents();

                // Thiết lập sự kiện cho form tạo đơn mua hộ
                setupCreateOrderEvents();

                // Thiết lập sự kiện cho modal
                setupModalEvents();

                // Thiết lập sự kiện cho dashboard
                setupDashboardEvents();

                // Khôi phục dữ liệu từ localStorage nếu có
                restoreFormData();

                // Bắt đầu tự động làm mới danh sách đơn mua hộ
                startAutoRefresh();
            } catch (error) {
                console.error('Lỗi khởi tạo trang:', error);
            }
        });

        // Bắt đầu tự động làm mới danh sách đơn mua hộ
        function startAutoRefresh() {
            // Hủy interval cũ nếu có
            if (window.autoRefreshInterval) {
                clearInterval(window.autoRefreshInterval);
            }

            // Thiết lập interval mới (làm mới mỗi 60 giây)
            window.autoRefreshInterval = setInterval(() => {
                if (document.getElementById('createOrderTab').style.display !== 'none') {
                    console.log('Tự động làm mới danh sách đơn mua hộ...');
                    loadPurchaseOrders();
                }
            }, 60000); // 60 giây

            console.log('Đã bật tự động làm mới danh sách đơn mua hộ (mỗi 60 giây)');
        }

        // Kiểm tra phiên đăng nhập
        async function checkSession(token) {
            try {
                // Thử kiểm tra phiên admin trước
                const adminResponse = await fetch(`${API_URL}/api/admin/check-session`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (adminResponse.ok) {
                    const data = await adminResponse.json();
                    isAdmin = true;
                    window.isAdmin = true; // Đảm bảo biến global được set
                    showAdminPanel(true, data.admin.username);
                    return;
                }

                // Nếu không phải admin, thử kiểm tra phiên nhân viên
                const staffResponse = await fetch(`${API_URL}/api/admin/staff/check-session`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (staffResponse.ok) {
                    const data = await staffResponse.json();
                    isAdmin = false;
                    window.isAdmin = false; // Đảm bảo biến global được set
                    showAdminPanel(false, data.staff.username);
                    return;
                }

                // Nếu không có phiên hợp lệ, hiển thị form đăng nhập
                showLoginForm();
            } catch (error) {
                console.error('Lỗi kiểm tra phiên đăng nhập:', error);
                showLoginForm();
            }
        }

        // Hiển thị form đăng nhập
        function showLoginForm() {
            document.getElementById('mainContent').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'block';
        }

        // Hàm ẩn tất cả các tab-content
        function hideAllTabContents() {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });
        }

        // Hiển thị giao diện chính
        function showAdminPanel(isAdminUser, username) {
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('mainContent').style.display = 'block';
            document.getElementById('userName').textContent = username;

            // Hiển thị tab quản lý đơn chỉ cho admin
            document.getElementById('manageOrdersTabButton').style.display = isAdminUser ? 'block' : 'none';

            // Hiển thị/ẩn dropdown nhân viên phụ trách dựa vào quyền
            const staffSelectContainer = document.getElementById('staffSelect').closest('.form-col');
            if (staffSelectContainer) {
                staffSelectContainer.style.display = isAdminUser ? 'block' : 'none';
            }

            // Ẩn tất cả các tab-content trước
            hideAllTabContents();

            // Hiển thị tab mặc định (createOrderTab)
            document.getElementById('createOrderTab').style.display = 'block';
            document.getElementById('createOrderTab').classList.add('active');

            // Đảm bảo tab "Tạo đơn mua hộ" được chọn
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelector('.tab[data-tab="createOrder"]').classList.add('active');

            // Tải dữ liệu
            loadData();

            // Tải tỷ giá USD
            loadUsdExchangeRate();
        }

        // Tải tỷ giá từ API
        async function loadUsdExchangeRate() {
            try {
                const response = await fetch(`${API_URL}/api/exchange-rates`);
                if (response.ok) {
                    const data = await response.json();

                    // Lưu trữ tất cả tỷ giá
                    if (data.vcb_rates && data.vcb_rates.length > 0) {
                        allExchangeRates = data.vcb_rates.map(rate => ({
                            id: rate.id,
                            name: `${rate.currency_code} - ${rate.currency_name.trim()}`,
                            currency_code: rate.currency_code,
                            rate: parseFloat(rate.sell),
                            type: 'vcb'
                        }));

                        // Tìm tỷ giá USD trong danh sách tỷ giá VCB
                        const usdRate = data.vcb_rates.find(rate => rate.currency_code === 'USD');
                        if (usdRate) {
                            // Lấy giá trị sell từ tỷ giá USD
                            usdExchangeRate = parseFloat(usdRate.sell);
                            console.log('Đã tải tỷ giá USD:', usdExchangeRate);
                        }
                    }

                    // Lưu trữ tỷ giá tùy chỉnh
                    if (data.custom_rates && data.custom_rates.length > 0) {
                        customExchangeRates = data.custom_rates.map(rate => ({
                            id: rate.id,
                            name: rate.name,
                            currency_code: rate.currency_code,
                            rate: parseFloat(rate.rate),
                            type: 'custom'
                        }));

                        // Thêm tỷ giá tùy chỉnh vào danh sách tất cả tỷ giá
                        allExchangeRates = [...allExchangeRates, ...customExchangeRates];
                    }

                    console.log('Đã tải tất cả tỷ giá:', allExchangeRates);
                }
            } catch (error) {
                console.error('Lỗi khi tải tỷ giá:', error);
            }
        }

        // Thiết lập sự kiện cho các tab
        function setupTabEvents() {
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // Xóa class active từ tất cả các tab
                    tabs.forEach(t => t.classList.remove('active'));

                    // Ẩn tất cả các tab-content
                    hideAllTabContents();

                    // Thêm class active cho tab được chọn
                    this.classList.add('active');

                    // Hiển thị nội dung tab tương ứng
                    const tabContent = document.getElementById(tabId + 'Tab');
                    if (tabContent) {
                        tabContent.classList.add('active');
                        tabContent.style.display = 'block';
                    }

                    // Tải dữ liệu cho tab nếu cần
                    if (tabId === 'manageOrders') {
                        // Đảm bảo hiển thị tab Quản lý đơn
                        document.getElementById('manageOrdersTab').style.display = 'block';
                        // Tải lại danh sách đơn hàng
                        loadPurchaseOrders();
                    } else if (tabId === 'dashboard') {
                        // Đảm bảo hiển thị tab Dashboard
                        document.getElementById('dashboardTab').style.display = 'block';
                        // Tải dữ liệu cho dashboard
                        loadDashboardData();
                    } else if (tabId === 'createOrder') {
                        // Initialize orders table enhancements when switching to createOrder tab
                        setTimeout(() => {
                            initializeOrdersTableEnhancements();
                        }, 100);
                    }
                });
            });
        }

        // Thiết lập sự kiện cho dashboard
        function setupDashboardEvents() {
            // Thiết lập ngày mặc định (30 ngày gần nhất)
            const today = new Date();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);

            // Format dates for input fields (YYYY-MM-DD)
            const formatDateForInput = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // Set default date values
            document.getElementById('dashboardStartDate').value = formatDateForInput(thirtyDaysAgo);
            document.getElementById('dashboardEndDate').value = formatDateForInput(today);

            // Save to dashboardData
            dashboardData.startDate = thirtyDaysAgo;
            dashboardData.endDate = today;

            // Apply date filter button
            document.getElementById('applyDateFilter').addEventListener('click', function() {
                const startDateStr = document.getElementById('dashboardStartDate').value;
                const endDateStr = document.getElementById('dashboardEndDate').value;

                if (startDateStr && endDateStr) {
                    dashboardData.startDate = new Date(startDateStr);
                    dashboardData.endDate = new Date(endDateStr);

                    // Set time to end of day for end date
                    dashboardData.endDate.setHours(23, 59, 59, 999);

                    // Reload dashboard data
                    loadDashboardData();
                } else {
                    alert('Vui lòng chọn khoảng thời gian hợp lệ');
                }
            });

            // Reset date filter button
            document.getElementById('resetDateFilter').addEventListener('click', function() {
                const today = new Date();
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(today.getDate() - 30);

                document.getElementById('dashboardStartDate').value = formatDateForInput(thirtyDaysAgo);
                document.getElementById('dashboardEndDate').value = formatDateForInput(today);

                dashboardData.startDate = thirtyDaysAgo;
                dashboardData.endDate = today;

                // Reload dashboard data
                loadDashboardData();
            });

            // Thiết lập sự kiện cho các nút chuyển đổi biểu đồ/danh sách trong tab Trạng thái đơn
            const orderStatusChartActions = document.querySelector('.dashboard-chart-container:nth-of-type(2) .chart-actions');
            if (orderStatusChartActions) {
                const chartButtons = orderStatusChartActions.querySelectorAll('.chart-action');
                const orderStatusChart = document.getElementById('orderStatusChart');
                const orderStatusList = document.querySelector('.dashboard-chart-container:nth-of-type(2) + .dashboard-status-list');

                chartButtons.forEach((button, index) => {
                    button.addEventListener('click', function() {
                        // Xóa class active từ tất cả các nút
                        chartButtons.forEach(btn => btn.classList.remove('active'));
                        // Thêm class active cho nút được click
                        this.classList.add('active');

                        if (index === 0) { // Biểu đồ
                            orderStatusChart.parentElement.style.display = 'block';
                            orderStatusList.style.display = 'block';
                        } else { // Danh sách
                            orderStatusChart.parentElement.style.display = 'none';
                            orderStatusList.style.display = 'block';

                            // Tạo bảng danh sách nếu chưa có
                            const listContainer = orderStatusList.querySelector('.status-table-container');
                            if (!listContainer) {
                                const tableContainer = document.createElement('div');
                                tableContainer.className = 'status-table-container';
                                tableContainer.style.marginTop = '15px';

                                const table = document.createElement('table');
                                table.className = 'dashboard-table';
                                table.innerHTML = `
                                    <thead>
                                        <tr>
                                            <th>Trạng thái</th>
                                            <th>Số lượng</th>
                                            <th>Tỷ lệ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><span class="status-dot orange"></span> Đơn mới</td>
                                            <td id="newOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot blue"></span> Đã cọc</td>
                                            <td id="depositedOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot purple"></span> Đang mua hàng</td>
                                            <td id="buyingOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot green"></span> Hoàn thành</td>
                                            <td id="completedOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot red"></span> Đã hủy</td>
                                            <td id="cancelledOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                    </tbody>
                                `;

                                tableContainer.appendChild(table);
                                orderStatusList.appendChild(tableContainer);
                            }
                        }
                    });
                });
            }

            // Thiết lập sự kiện cho các nút chuyển đổi biểu đồ/danh sách trong tab Thanh toán
            const paymentStatusChartActions = document.querySelector('.dashboard-chart-container:nth-of-type(3) .chart-actions');
            if (paymentStatusChartActions) {
                const chartButtons = paymentStatusChartActions.querySelectorAll('.chart-action');
                const paymentStatusChart = document.getElementById('paymentStatusChart');
                const paymentStatusList = document.querySelector('.dashboard-chart-container:nth-of-type(3) + .dashboard-status-list');

                chartButtons.forEach((button, index) => {
                    button.addEventListener('click', function() {
                        // Xóa class active từ tất cả các nút
                        chartButtons.forEach(btn => btn.classList.remove('active'));
                        // Thêm class active cho nút được click
                        this.classList.add('active');

                        if (index === 0) { // Biểu đồ
                            paymentStatusChart.parentElement.style.display = 'block';
                            paymentStatusList.style.display = 'block';
                        } else { // Danh sách
                            paymentStatusChart.parentElement.style.display = 'none';
                            paymentStatusList.style.display = 'block';

                            // Tạo bảng danh sách nếu chưa có
                            const listContainer = paymentStatusList.querySelector('.status-table-container');
                            if (!listContainer) {
                                const tableContainer = document.createElement('div');
                                tableContainer.className = 'status-table-container';
                                tableContainer.style.marginTop = '15px';

                                const table = document.createElement('table');
                                table.className = 'dashboard-table';
                                table.innerHTML = `
                                    <thead>
                                        <tr>
                                            <th>Trạng thái</th>
                                            <th>Số lượng</th>
                                            <th>Tỷ lệ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><span class="status-dot orange"></span> Chưa thanh toán</td>
                                            <td id="unpaidOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot blue"></span> Đã cọc</td>
                                            <td id="depositedPaymentCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                        <tr>
                                            <td><span class="status-dot green"></span> Đã thanh toán đủ</td>
                                            <td id="paidOrdersCount2">0</td>
                                            <td>0%</td>
                                        </tr>
                                    </tbody>
                                `;

                                tableContainer.appendChild(table);
                                paymentStatusList.appendChild(tableContainer);
                            }
                        }
                    });
                });
            }
        }

        // Thiết lập sự kiện cho form đăng nhập
        function setupLoginEvents() {
            // Chuyển đổi giữa đăng nhập admin và nhân viên
            document.getElementById('adminLoginOption').addEventListener('click', function() {
                document.getElementById('staffLoginOption').classList.remove('active');
                this.classList.add('active');
            });

            document.getElementById('staffLoginOption').addEventListener('click', function() {
                document.getElementById('adminLoginOption').classList.remove('active');
                this.classList.add('active');
            });

            // Xử lý đăng nhập
            document.getElementById('loginBtn').addEventListener('click', login);

            // Cho phép nhấn Enter để đăng nhập
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });

            // Xử lý đăng xuất
            document.getElementById('logoutBtn').addEventListener('click', function() {
                localStorage.removeItem('admin_token');
                localStorage.removeItem('staff_token');
                window.location.reload();
            });
        }

        // Hàm đăng nhập
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginType = document.querySelector('.login-type-option.active').getAttribute('data-type');

            if (!username || !password) {
                alert('Vui lòng nhập đầy đủ thông tin đăng nhập');
                return;
            }

            showLoader(true, 'Đang đăng nhập...');

            try {
                const loginData = {
                    username: username,
                    password: password
                };

                console.log("Dữ liệu gửi đi:", loginData);

                const endpoint = loginType === 'admin' ?
                    `${API_URL}/api/admin/login` :
                    `${API_URL}/api/admin/staff/login`;

                console.log("Endpoint:", endpoint);

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                // Log thông tin response để debug
                console.log("Status:", response.status);

                if (response.ok) {
                    const data = await response.json();

                    if (loginType === 'admin') {
                        localStorage.setItem('admin_token', data.token);
                        isAdmin = true;
                        window.isAdmin = true; // Đảm bảo biến global được set
                        showAdminPanel(true, data.admin.username);
                    } else {
                        localStorage.setItem('staff_token', data.token);
                        isAdmin = false;
                        window.isAdmin = false; // Đảm bảo biến global được set
                        showAdminPanel(false, data.staff.username);
                    }
                } else {
                    const errorData = await response.json();
                    alert(errorData.message || 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin đăng nhập.');
                }
            } catch (error) {
                console.error('Lỗi đăng nhập:', error);
                alert('Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Tải dữ liệu
        async function loadData() {
            showLoader(true, 'Đang tải dữ liệu...');
            try {
                // Tải song song tất cả dữ liệu
                await Promise.all([
                    loadStaff(),
                    loadUsers(),
                    loadWarehouses()
                ]);

                // Cập nhật các dropdown sau khi tải dữ liệu
                updateUserSelect();
                updateWarehouseSelect();
                updateStaffSelect();

                // Tải danh sách đơn mua hộ cho cả admin và nhân viên
                loadPurchaseOrders();

                console.log('Đã tải xong tất cả dữ liệu và cập nhật dropdown');
            } catch (error) {
                console.error('Lỗi khi tải dữ liệu:', error);
            } finally {
                showLoader(false);
            }
        }

        // Tải danh sách nhân viên
        async function loadStaff() {
            try {
                // Nếu không phải admin, không cần tải danh sách nhân viên
                if (!isAdmin) {
                    // Lấy thông tin nhân viên đang đăng nhập
                    const token = localStorage.getItem('staff_token');
                    if (token) {
                        const staffData = JSON.parse(atob(token.split('.')[1]));
                        currentStaffId = staffData.staffId;
                        console.log('Nhân viên đang đăng nhập:', currentStaffId);
                    }
                    return;
                }

                const response = await fetch(`${API_URL}/api/admin/staff`, {
                    headers: getAuthHeader()
                });

                if (response.ok) {
                    const staff = await response.json();
                    allStaff = staff;
                    console.log('Danh sách nhân viên:', staff);
                    return staff;
                } else {
                    console.error('Không thể tải danh sách nhân viên');
                    return [];
                }
            } catch (error) {
                console.error('Lỗi tải danh sách nhân viên:', error);
                return [];
            }
        }

        // Cập nhật dropdown chọn nhân viên
        function updateStaffSelect() {
            const staffSelect = document.getElementById('staffSelect');
            if (!staffSelect) return;

            // Xóa tất cả các option hiện tại
            staffSelect.innerHTML = '';

            // Thêm option mặc định
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Chọn nhân viên phụ trách';
            staffSelect.appendChild(defaultOption);

            // Thêm các option nhân viên
            allStaff.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = staff.fullname || staff.username;

                // Nếu là nhân viên đang đăng nhập, chọn mặc định
                if (staff.id === currentStaffId) {
                    option.selected = true;
                }

                staffSelect.appendChild(option);
            });

            // Khởi tạo lại Select2 nếu có
            try {
                $('#staffSelect').select2({
                    width: '100%',
                    placeholder: 'Chọn nhân viên phụ trách'
                });
            } catch (error) {
                console.error('Lỗi khởi tạo Select2 cho staffSelect:', error);
            }
        }

        // Tải danh sách users
        async function loadUsers() {
            try {
                console.log('Đang gọi API users...');
                const response = await fetch(`${API_URL}/api/admin/users`, {
                    headers: getAuthHeader()
                });

                console.log('Kết quả API users:', response.status, response.statusText);

                if (response.ok) {
                    const users = await response.json();
                    allUsers = users;
                    console.log('Dữ liệu users từ API:', users);
                    return users;
                } else {
                    console.error('Không thể tải danh sách users');
                    return [];
                }
            } catch (error) {
                console.error('Lỗi tải danh sách users:', error);
                return [];
            }
        }

        // Tải danh sách kho
        async function loadWarehouses() {
            try {
                console.log('Đang gọi API warehouses...');
                const response = await fetch(`${API_URL}/api/admin/warehouses`, {
                    headers: getAuthHeader()
                });

                console.log('Kết quả API warehouses:', response.status, response.statusText);

                if (response.ok) {
                    const warehouses = await response.json();
                    allWarehouses = warehouses;
                    console.log('Dữ liệu warehouses từ API:', warehouses);
                    return warehouses;
                } else {
                    console.error('Không thể tải danh sách warehouses');
                    return [];
                }
            } catch (error) {
                console.error('Lỗi tải danh sách warehouses:', error);
                return [];
            }
        }

        // Cập nhật dropdown chọn user
        function updateUserSelect() {
            const userSelect = document.getElementById('userSelect');
            userSelect.innerHTML = '<option value="">Chọn khách hàng...</option>';

            allUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.username}${user.phone ? ` - ${user.phone}` : ''}`;
                userSelect.appendChild(option);
            });

            // Khởi tạo lại Select2
            $('#userSelect').select2({
                width: '100%',
                placeholder: 'Chọn khách hàng...'
            });
        }

        // Cập nhật dropdown chọn kho
        function updateWarehouseSelect() {
            const warehouseSelect = document.getElementById('warehouseSelect');
            if (!warehouseSelect) return;

            warehouseSelect.innerHTML = '<option value="">Chọn kho...</option>';

            allWarehouses.forEach(warehouse => {
                const option = document.createElement('option');
                option.value = warehouse.id;
                option.textContent = warehouse.name;
                warehouseSelect.appendChild(option);
            });

            // Khởi tạo lại Select2
            $('#warehouseSelect').select2({
                width: '100%',
                placeholder: 'Chọn kho...'
            });
        }

        // Lấy header xác thực
        function getAuthHeader() {
            const token = localStorage.getItem('admin_token') || localStorage.getItem('staff_token');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // Thiết lập sự kiện cho form tạo đơn mua hộ
        function setupCreateOrderEvents() {
            // Xử lý nút tiếp tục để hiển thị form
            document.getElementById('continueToFormBtn').addEventListener('click', function() {
                // Xóa tất cả thông báo lỗi cũ
                clearAllErrorMessages();

                const userId = document.getElementById('userSelect').value;
                if (userId) {
                    document.getElementById('purchaseOrderForm').style.display = 'block';

                    // Thêm dòng đầu tiên nếu chưa có
                    if (purchaseOrderItems.length === 0) {
                        addItemRow();
                    }

                    // Cuộn xuống form
                    document.getElementById('purchaseOrderForm').scrollIntoView({ behavior: 'smooth' });
                } else {
                    // Hiển thị lỗi trực tiếp trên trường thay vì alert
                    showFieldError('userSelect', 'Vui lòng chọn khách hàng trước khi tiếp tục');
                }
            });

            // Hiển thị form khi chọn user (tùy chọn)
            document.getElementById('userSelect').addEventListener('change', function() {
                const userId = this.value;
                if (!userId) {
                    document.getElementById('purchaseOrderForm').style.display = 'none';
                }
            });

            // Hàm kiểm tra và cập nhật dữ liệu sản phẩm
            function validateAndUpdateProducts() {
                // Đảm bảo tất cả các trường input đã cập nhật vào mảng purchaseOrderItems
                const rows = document.querySelectorAll('#itemsTableBody tr');

                // Tạo mảng mới để lưu dữ liệu sản phẩm đã cập nhật
                const updatedItems = [];

                rows.forEach((row, rowIndex) => {
                    // Lấy các input theo ID cụ thể
                    const linkInput = document.getElementById(`product-link-${rowIndex}`);
                    const nameInput = document.getElementById(`product-name-${rowIndex}`);
                    const variantInput = document.getElementById(`product-variant-${rowIndex}`);
                    const quantityInput = document.getElementById(`product-quantity-${rowIndex}`);
                    const priceInput = document.getElementById(`product-price-${rowIndex}`);

                    // Tạo đối tượng sản phẩm mới với giá trị từ các input
                    const item = {
                        product_link: linkInput ? linkInput.value || '' : '',
                        product_name: nameInput ? nameInput.value || '' : '',
                        product_variant: variantInput ? variantInput.value || '' : '',
                        quantity: quantityInput ? (!isNaN(parseFloat(quantityInput.value)) ? parseFloat(quantityInput.value) : 1) : 1,
                        price: priceInput ? (!isNaN(parseFloat(priceInput.value)) ? parseFloat(priceInput.value) : 0) : 0
                    };

                    console.log(`Đọc dữ liệu từ dòng ${rowIndex}:`, JSON.stringify(item));

                    // Thêm vào mảng mới
                    updatedItems.push(item);
                });

                // Cập nhật lại mảng purchaseOrderItems
                purchaseOrderItems = updatedItems;

                // Xóa các trường không cần thiết
                purchaseOrderItems.forEach(item => {
                    // Xóa các trường name, variant nếu có (để tránh nhầm lẫn)
                    delete item.name;
                    delete item.variant;
                    delete item.link;
                });

                // Ghi log để debug
                console.log('Dữ liệu sản phẩm sau khi cập nhật:', JSON.stringify(purchaseOrderItems));
            }

            // Xử lý nút tạo đơn mua hộ
            document.getElementById('createOrderBtn').addEventListener('click', function() {
                // Cập nhật dữ liệu sản phẩm trước khi kiểm tra
                validateAndUpdateProducts();

                // Tạo đơn mua hộ
                createPurchaseOrder();
            });

            // Lưu dữ liệu vào localStorage khi nhập liệu
            document.getElementById('exchangeRate').addEventListener('input', saveFormData);
            document.getElementById('depositPercentage').addEventListener('input', saveFormData);
            document.getElementById('note').addEventListener('input', saveFormData);

            // Xử lý nút thêm đơn mua hộ mới
            document.getElementById('addNewOrderBtn').addEventListener('click', function() {
                // Ẩn tất cả các tab-content
                hideAllTabContents();

                // Hiển thị form tạo đơn mua hộ mới
                document.getElementById('createOrderFormTab').style.display = 'block';

                // Reset form
                document.getElementById('userSelect').value = '';
                document.getElementById('exchangeRate').value = '';
                document.getElementById('depositPercentage').value = '';
                document.getElementById('note').value = '';
                purchaseOrderItems = [];
                updateItemsTable();

                // Ẩn form chi tiết
                document.getElementById('purchaseOrderForm').style.display = 'none';

                // Khởi tạo lại Select2
                $('#userSelect').trigger('change');
            });

            // Xử lý nút quay lại danh sách đơn mua hộ
            document.getElementById('backToOrderListBtn').addEventListener('click', function() {
                // Ẩn tất cả các tab-content
                hideAllTabContents();

                // Hiển thị tab danh sách đơn mua hộ
                document.getElementById('createOrderTab').style.display = 'block';
                document.getElementById('createOrderTab').classList.add('active');

                // Đảm bảo tab "Tạo đơn mua hộ" được chọn
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelector('.tab[data-tab="createOrder"]').classList.add('active');

                // Tải lại danh sách đơn mua hộ
                loadPurchaseOrders();
            });

            // Thiết lập sự kiện cho bộ lọc đơn mua hộ
            setupOrderFilterEvents();
        }

        // Thiết lập sự kiện cho bộ lọc đơn mua hộ
        function setupOrderFilterEvents() {
            // Sự kiện cho ô tìm kiếm
            const searchInput = document.getElementById('orderSearchInput');
            if (searchInput) {
                searchInput.addEventListener('input', applyOrderFilter);
            }

            // Sự kiện cho ô tìm kiếm trong tab quản lý đơn
            const manageOrderSearchInput = document.getElementById('manageOrderSearchInput');
            if (manageOrderSearchInput) {
                manageOrderSearchInput.addEventListener('input', applyOrderFilter);
            }

            // Sự kiện cho các dropdown
            const statusFilter = document.getElementById('orderStatusFilter');
            if (statusFilter) {
                statusFilter.addEventListener('change', applyOrderFilter);
            }

            const paymentStatusFilter = document.getElementById('paymentStatusFilter');
            if (paymentStatusFilter) {
                paymentStatusFilter.addEventListener('change', applyOrderFilter);
            }

            // Sự kiện cho các trường ngày
            const startDateFilter = document.getElementById('orderStartDate');
            if (startDateFilter) {
                startDateFilter.addEventListener('change', applyOrderFilter);
            }

            const endDateFilter = document.getElementById('orderEndDate');
            if (endDateFilter) {
                endDateFilter.addEventListener('change', applyOrderFilter);
            }

            // Sự kiện cho nút đặt lại bộ lọc
            const resetFilterBtn = document.getElementById('orderResetFilterBtn');
            if (resetFilterBtn) {
                resetFilterBtn.addEventListener('click', function() {
                    // Đặt lại tất cả các trường lọc
                    if (searchInput) searchInput.value = '';
                    if (statusFilter) statusFilter.value = '';
                    if (paymentStatusFilter) paymentStatusFilter.value = '';
                    if (startDateFilter) startDateFilter.value = '';
                    if (endDateFilter) endDateFilter.value = '';

                    // Áp dụng bộ lọc (hiển thị tất cả)
                    applyOrderFilter();
                });
            }
        }

        // Hàm hiển thị lỗi chung
        function showGeneralError(message) {
            const errorContainer = document.getElementById('generalErrorContainer');
            if (errorContainer) {
                errorContainer.textContent = message;
                errorContainer.style.display = 'block';

                // Tự động ẩn sau 5 giây
                setTimeout(() => {
                    errorContainer.style.display = 'none';
                }, 5000);
            }
        }

        // Hàm hiển thị lỗi cho trường cụ thể
        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');

            if (field) {
                field.classList.add('error-field');
            }

            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        // Hàm hiển thị lỗi cho input
        function showInputError(inputElement, message) {
            if (!inputElement) return;

            inputElement.classList.add('error-field');
            inputElement.classList.add('input-error');
            inputElement.setAttribute('data-error', message);
        }

        // Hàm xóa lỗi cho input
        function clearInputError(inputElement) {
            if (!inputElement) return;

            inputElement.classList.remove('error-field');
            inputElement.classList.remove('input-error');
            inputElement.removeAttribute('data-error');
        }

        // Hàm xóa tất cả thông báo lỗi
        function clearAllErrorMessages() {
            // Xóa lỗi chung
            const generalErrorContainer = document.getElementById('generalErrorContainer');
            if (generalErrorContainer) {
                generalErrorContainer.textContent = '';
                generalErrorContainer.style.display = 'none';
            }

            // Xóa lỗi các trường
            const errorMessages = document.querySelectorAll('.error-message');
            errorMessages.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });

            // Xóa lỗi các input
            const errorFields = document.querySelectorAll('.error-field');
            errorFields.forEach(element => {
                element.classList.remove('error-field');
            });

            const inputErrors = document.querySelectorAll('.input-error');
            inputErrors.forEach(element => {
                element.classList.remove('input-error');
                element.removeAttribute('data-error');
            });

            // Xóa lỗi bảng sản phẩm
            const itemsErrorContainer = document.getElementById('itemsErrorContainer');
            if (itemsErrorContainer) {
                itemsErrorContainer.textContent = '';
            }
        }

        // Thêm dòng mới vào bảng sản phẩm
        function addItemRow() {
            const tbody = document.getElementById('itemsTableBody');
            const rowIndex = purchaseOrderItems.length;

            // Kiểm tra nếu đã có dòng trống cuối cùng thì không thêm dòng mới
            // Bỏ kiểm tra này để luôn thêm dòng mới khi cần

            // Thêm item mới vào mảng
            purchaseOrderItems.push({
                product_link: '',
                product_name: '',
                product_variant: '',
                quantity: 1,
                price: 0
            });

            // Tạo dòng mới
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="text" class="product-link" id="product-link-${rowIndex}" data-index="${rowIndex}" placeholder="Link sản phẩm">
                </td>
                <td>
                    <input type="text" class="product-name" id="product-name-${rowIndex}" data-index="${rowIndex}" placeholder="Tên sản phẩm" required>
                </td>
                <td>
                    <input type="text" class="product-variant" id="product-variant-${rowIndex}" data-index="${rowIndex}" placeholder="Phân loại">
                </td>
                <td>
                    <input type="number" class="product-quantity" id="product-quantity-${rowIndex}" data-index="${rowIndex}" min="1" value="1" required>
                </td>
                <td>
                    <input type="number" class="product-price" id="product-price-${rowIndex}" data-index="${rowIndex}" step="0.01" min="0" value="0" required>
                </td>
                <td>
                    <button type="button" class="remove-row-btn" data-index="${rowIndex}">X</button>
                </td>
            `;

            tbody.appendChild(row);

            // Thêm sự kiện cho các input
            const inputs = row.querySelectorAll('input');
            inputs.forEach(input => {
                // Xóa lỗi khi focus vào input
                input.addEventListener('focus', function() {
                    clearInputError(this);
                });

                // Cập nhật giá trị ban đầu vào mảng
                const initialIndex = parseInt(input.getAttribute('data-index'));
                const initialField = input.classList[0].replace('product-', '');

                if (initialIndex >= 0 && initialIndex < purchaseOrderItems.length && initialField) {
                    if (initialField === 'quantity') {
                        const value = parseFloat(input.value);
                        purchaseOrderItems[initialIndex][initialField] = !isNaN(value) ? value : 1;
                    } else if (initialField === 'price') {
                        const value = parseFloat(input.value);
                        purchaseOrderItems[initialIndex][initialField] = !isNaN(value) ? value : 0;
                    } else {
                        purchaseOrderItems[initialIndex][initialField] = input.value || '';
                    }
                }

                input.addEventListener('input', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    const field = this.classList[0].replace('product-', '');
                    const value = this.value;

                    console.log(`Input event: field=${field}, index=${index}, value=${value}`);

                    // Đảm bảo index hợp lệ
                    if (index >= 0 && index < purchaseOrderItems.length) {
                        // Đảm bảo đối tượng sản phẩm tồn tại và có cấu trúc đúng
                        if (!purchaseOrderItems[index]) {
                            purchaseOrderItems[index] = {
                                product_link: '',
                                product_name: '',
                                product_variant: '',
                                quantity: 1,
                                price: 0
                            };
                        }

                        // Cập nhật giá trị vào mảng theo field
                        switch(field) {
                            case 'link':
                                purchaseOrderItems[index].product_link = value;
                                break;
                            case 'name':
                                purchaseOrderItems[index].product_name = value;
                                break;
                            case 'variant':
                                purchaseOrderItems[index].product_variant = value;
                                break;
                            case 'quantity':
                                const qtyValue = parseFloat(value);
                                purchaseOrderItems[index].quantity = !isNaN(qtyValue) ? qtyValue : 1;
                                break;
                            case 'price':
                                const priceValue = parseFloat(value);
                                purchaseOrderItems[index].price = !isNaN(priceValue) ? priceValue : 0;
                                break;
                            default:
                                console.log(`Field không xác định: ${field}`);
                        }

                        // Xóa các trường không cần thiết
                        delete purchaseOrderItems[index].name;
                        delete purchaseOrderItems[index].variant;
                        delete purchaseOrderItems[index].link;

                        // Log để debug
                        //console.log(`Đã cập nhật ${field} cho sản phẩm ${index}:`, JSON.stringify(purchaseOrderItems[index]));
                    }

                    saveFormData();

                    // Xóa lỗi khi nhập liệu
                    clearInputError(this);

                    // Tự động thêm dòng mới khi nhập dòng cuối cùng và đã có dữ liệu
                    if (index === purchaseOrderItems.length - 1) {
                        // Thêm dòng mới nếu người dùng đã nhập bất kỳ thông tin nào
                        if (this.value.trim() !== '') {
                            // Đợi một chút để đảm bảo dữ liệu đã được cập nhật
                            setTimeout(() => {
                                addItemRow();
                            }, 100);
                        }
                    }
                });
            });

            // Thêm sự kiện cho nút xóa
            const removeBtn = row.querySelector('.remove-row-btn');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    removeItemRow(index);
                });
            }
        }

        // Xóa dòng khỏi bảng sản phẩm
        function removeItemRow(index) {
            // Không cho phép xóa nếu chỉ còn 1 dòng
            if (purchaseOrderItems.length <= 1) {
                // Thay vì xóa, hãy làm trống dòng
                purchaseOrderItems[0] = {
                    product_link: '',
                    product_name: '',
                    product_variant: '',
                    quantity: 1,
                    price: 0
                };
                updateItemsTable();
                saveFormData();
                return;
            }

            // Xóa item khỏi mảng
            purchaseOrderItems.splice(index, 1);

            // Cập nhật lại bảng
            updateItemsTable();

            // Lưu dữ liệu
            saveFormData();

            // Xóa thông báo lỗi liên quan đến sản phẩm
            document.getElementById('itemsErrorContainer').textContent = '';

            // Đảm bảo luôn có một dòng trống ở cuối
            const lastItem = purchaseOrderItems[purchaseOrderItems.length - 1];
            if (lastItem && (lastItem.product_name || lastItem.product_variant || lastItem.product_link ||
                lastItem.quantity > 1 || lastItem.price > 0)) {
                addItemRow();
            }
        }

        // Cập nhật lại bảng sản phẩm
        function updateItemsTable() {
            const tbody = document.getElementById('itemsTableBody');
            tbody.innerHTML = '';

            purchaseOrderItems.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="text" class="product-link" id="product-link-${index}" data-index="${index}" placeholder="Link sản phẩm" value="${item.product_link || ''}">
                    </td>
                    <td>
                        <input type="text" class="product-name" id="product-name-${index}" data-index="${index}" placeholder="Tên sản phẩm" value="${item.product_name || ''}" required>
                    </td>
                    <td>
                        <input type="text" class="product-variant" id="product-variant-${index}" data-index="${index}" placeholder="Phân loại" value="${item.product_variant || ''}">
                    </td>
                    <td>
                        <input type="number" class="product-quantity" id="product-quantity-${index}" data-index="${index}" min="1" value="${item.quantity || 1}" required>
                    </td>
                    <td>
                        <input type="number" class="product-price" id="product-price-${index}" data-index="${index}" step="0.01" min="0" value="${item.price || 0}" required>
                    </td>
                    <td>
                        <button type="button" class="remove-row-btn" data-index="${index}">X</button>
                    </td>
                `;

                tbody.appendChild(row);

                // Thêm sự kiện cho các input
                const inputs = row.querySelectorAll('input');
                inputs.forEach(input => {
                    // Xóa lỗi khi focus vào input
                    input.addEventListener('focus', function() {
                        clearInputError(this);
                    });

                    // Cập nhật giá trị ban đầu vào mảng
                    const initialIdx = parseInt(input.getAttribute('data-index'));
                    const initialField = input.classList[0].replace('product-', '');

                    if (initialIdx >= 0 && initialIdx < purchaseOrderItems.length && initialField) {
                        if (initialField === 'quantity') {
                            const value = parseFloat(input.value);
                            purchaseOrderItems[initialIdx][initialField] = !isNaN(value) ? value : 1;
                        } else if (initialField === 'price') {
                            const value = parseFloat(input.value);
                            purchaseOrderItems[initialIdx][initialField] = !isNaN(value) ? value : 0;
                        } else {
                            purchaseOrderItems[initialIdx][initialField] = input.value || '';
                        }

                        // Log để debug
                        console.log(`Khởi tạo ${initialField} cho sản phẩm ${initialIdx}:`, JSON.stringify(purchaseOrderItems[initialIdx]));
                    }

                    input.addEventListener('input', function() {
                        const idx = parseInt(this.getAttribute('data-index'));
                        const field = this.classList[0].replace('product-', '');
                        const value = this.value;

                        console.log(`Input event trong updateItemsTable: field=${field}, index=${idx}, value=${value}`);

                        // Đảm bảo index hợp lệ
                        if (idx >= 0 && idx < purchaseOrderItems.length) {
                            // Đảm bảo đối tượng sản phẩm tồn tại và có cấu trúc đúng
                            if (!purchaseOrderItems[idx]) {
                                purchaseOrderItems[idx] = {
                                    product_link: '',
                                    product_name: '',
                                    product_variant: '',
                                    quantity: 1,
                                    price: 0
                                };
                            }

                            // Cập nhật giá trị vào mảng theo field
                            switch(field) {
                                case 'link':
                                    purchaseOrderItems[idx].product_link = value;
                                    break;
                                case 'name':
                                    purchaseOrderItems[idx].product_name = value;
                                    break;
                                case 'variant':
                                    purchaseOrderItems[idx].product_variant = value;
                                    break;
                                case 'quantity':
                                    const qtyValue = parseFloat(value);
                                    purchaseOrderItems[idx].quantity = !isNaN(qtyValue) ? qtyValue : 1;
                                    break;
                                case 'price':
                                    const priceValue = parseFloat(value);
                                    purchaseOrderItems[idx].price = !isNaN(priceValue) ? priceValue : 0;
                                    break;
                                default:
                                    console.log(`Field không xác định trong updateItemsTable: ${field}`);
                            }

                            // Xóa các trường không cần thiết
                            delete purchaseOrderItems[idx].name;
                            delete purchaseOrderItems[idx].variant;
                            delete purchaseOrderItems[idx].link;

                            // Log để debug
                            console.log(`Đã cập nhật ${field} cho sản phẩm ${idx} trong updateItemsTable:`, JSON.stringify(purchaseOrderItems[idx]));
                        }

                        saveFormData();

                        // Xóa lỗi khi nhập liệu
                        clearInputError(this);

                        // Tự động thêm dòng mới khi nhập dòng cuối cùng và đã có dữ liệu
                        if (idx === purchaseOrderItems.length - 1) {
                            // Thêm dòng mới nếu người dùng đã nhập bất kỳ thông tin nào
                            if (this.value.trim() !== '') {
                                // Đợi một chút để đảm bảo dữ liệu đã được cập nhật
                                setTimeout(() => {
                                    addItemRow();
                                }, 100);
                            }
                        }
                    });
                });

                // Thêm sự kiện cho nút xóa
                const removeBtn = row.querySelector('.remove-row-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', function() {
                        const idx = parseInt(this.getAttribute('data-index'));
                        removeItemRow(idx);
                    });
                }
            });

            // Thêm dòng mới nếu không có dòng nào
            if (purchaseOrderItems.length === 0) {
                addItemRow();
            }
        }

        // Lưu dữ liệu form vào localStorage
        function saveFormData() {
            // Đảm bảo dữ liệu sản phẩm hợp lệ trước khi lưu
            const validItems = [];

            purchaseOrderItems.forEach((item, index) => {
                // Đảm bảo item có cấu trúc đúng
                if (!item) {
                    console.log(`Bỏ qua sản phẩm ${index} không hợp lệ khi lưu`);
                    return;
                }

                // Tạo bản sao của item để tránh tham chiếu
                const validItem = {
                    product_link: item.product_link || '',
                    product_name: item.product_name || '',
                    product_variant: item.product_variant || '',
                    quantity: typeof item.quantity === 'number' ? item.quantity : 1,
                    price: typeof item.price === 'number' ? item.price : 0
                };

                validItems.push(validItem);
            });

            const formData = {
                userId: document.getElementById('userSelect').value,
                staffId: document.getElementById('staffSelect').value || currentStaffId,
                exchangeRate: document.getElementById('exchangeRate').value,
                depositPercentage: document.getElementById('depositPercentage').value,
                note: document.getElementById('note').value,
                items: validItems
            };

            console.log('Lưu dữ liệu form:', JSON.stringify(formData));
            localStorage.setItem('purchaseOrderFormData', JSON.stringify(formData));
        }

        // Khôi phục dữ liệu form từ localStorage
        function restoreFormData() {
            const savedData = localStorage.getItem('purchaseOrderFormData');
            if (!savedData) return;

            try {
                const formData = JSON.parse(savedData);
                console.log('Khôi phục dữ liệu form từ localStorage:', formData);

                // Khôi phục dữ liệu
                if (formData.userId) {
                    document.getElementById('userSelect').value = formData.userId;
                    // Kích hoạt sự kiện change để hiển thị form
                    $('#userSelect').trigger('change');
                }

                if (formData.staffId) {
                    document.getElementById('staffSelect').value = formData.staffId;
                    // Kích hoạt sự kiện change
                    $('#staffSelect').trigger('change');
                }

                if (formData.exchangeRate) {
                    document.getElementById('exchangeRate').value = formData.exchangeRate;
                }

                if (formData.depositPercentage) {
                    document.getElementById('depositPercentage').value = formData.depositPercentage;
                }

                if (formData.note) {
                    document.getElementById('note').value = formData.note;
                }

                if (formData.items && formData.items.length > 0) {
                    // Đảm bảo các item có cấu trúc đúng
                    const validItems = [];

                    formData.items.forEach(item => {
                        if (!item) return;

                        // Tạo bản sao của item với các giá trị mặc định nếu cần
                        const validItem = {
                            product_link: item.product_link || '',
                            product_name: item.product_name || '',
                            product_variant: item.product_variant || '',
                            quantity: typeof item.quantity === 'number' ? item.quantity : 1,
                            price: typeof item.price === 'number' ? item.price : 0
                        };

                        validItems.push(validItem);
                    });

                    purchaseOrderItems = validItems;
                    console.log('Đã khôi phục dữ liệu sản phẩm:', JSON.stringify(purchaseOrderItems));
                    updateItemsTable();
                }
            } catch (error) {
                console.error('Lỗi khôi phục dữ liệu form:', error);
                localStorage.removeItem('purchaseOrderFormData');
            }
        }

        // Tạo đơn mua hộ mới
        async function createPurchaseOrder() {
            // Xóa tất cả thông báo lỗi cũ
            clearAllErrorMessages();

            // Kiểm tra dữ liệu đầu vào
            const userId = document.getElementById('userSelect').value;
            const exchangeRate = parseFloat(document.getElementById('exchangeRate').value);
            const depositPercentage = parseFloat(document.getElementById('depositPercentage').value);
            const note = document.getElementById('note').value;

            let isValid = true;

            if (!userId) {
                showFieldError('userSelect', 'Vui lòng chọn khách hàng');
                isValid = false;
            }

            if (!exchangeRate || exchangeRate <= 0) {
                showFieldError('exchangeRate', 'Vui lòng nhập tỷ giá hợp lệ');
                isValid = false;
            }

            if (!depositPercentage || depositPercentage < 0 || depositPercentage > 100) {
                showFieldError('depositPercentage', 'Vui lòng nhập tỷ lệ cọc hợp lệ (0-100%)');
                isValid = false;
            }

            // Kiểm tra các mục sản phẩm
            const validItems = [];

            // Lọc bỏ dòng trống và kiểm tra chi tiết
            const itemsToCheck = [];
            let hasIncompleteItems = false;

            // Kiểm tra từng sản phẩm và ghi log để debug
            purchaseOrderItems.forEach((item, index) => {
                // Đảm bảo item có cấu trúc đúng
                if (!item) {
                    console.log(`Sản phẩm ${index}: Không hợp lệ (null hoặc undefined)`);
                    return;
                }

                // Đảm bảo các thuộc tính tồn tại
                item.product_name = item.product_name || "";
                item.product_variant = item.product_variant || "";
                item.product_link = item.product_link || "";
                item.quantity = typeof item.quantity === 'number' ? item.quantity : 1;
                item.price = typeof item.price === 'number' ? item.price : 0;

                console.log(`Kiểm tra sản phẩm ${index}:`, JSON.stringify(item));

                // Bỏ qua dòng trống cuối cùng
                if (index === purchaseOrderItems.length - 1 &&
                    (!item.product_name || item.product_name.trim() === '') &&
                    (!item.product_variant || item.product_variant.trim() === '') &&
                    (!item.product_link || item.product_link.trim() === '') &&
                    (item.quantity <= 1) &&
                    (item.price <= 0)) {
                    console.log(`Bỏ qua dòng trống cuối cùng ${index}`);
                    return;
                }

                // Kiểm tra từng trường
                const hasName = item.product_name && item.product_name.trim() !== '';
                const hasValidQuantity = item.quantity > 0;
                const hasValidPrice = item.price > 0;

                console.log(`Sản phẩm ${index}: Tên=${hasName}, Số lượng=${hasValidQuantity}, Giá=${hasValidPrice}`);

                if (hasName && hasValidQuantity && hasValidPrice) {
                    itemsToCheck.push(item);
                } else if (hasName || hasValidQuantity || hasValidPrice) {
                    // Sản phẩm đã có một số thông tin nhưng chưa đầy đủ
                    hasIncompleteItems = true;
                }
            });

            console.log(`Số sản phẩm hợp lệ: ${itemsToCheck.length}, Có sản phẩm chưa hoàn thiện: ${hasIncompleteItems}`);

            if (itemsToCheck.length === 0) {
                if (hasIncompleteItems) {
                    showGeneralError('Vui lòng điền đầy đủ thông tin cho các sản phẩm');
                    document.getElementById('itemsErrorContainer').textContent = 'Vui lòng nhập đầy đủ thông tin sản phẩm (tên, số lượng, giá)';
                } else {
                    showGeneralError('Vui lòng thêm ít nhất một sản phẩm hợp lệ');
                    document.getElementById('itemsErrorContainer').textContent = 'Vui lòng nhập đầy đủ thông tin sản phẩm (tên, số lượng, giá)';
                }
                isValid = false;
            }

            // Kiểm tra chi tiết từng sản phẩm và hiển thị lỗi
            purchaseOrderItems.forEach((item, index) => {
                // Bỏ qua dòng trống cuối cùng
                if (index === purchaseOrderItems.length - 1 &&
                    (!item.product_name || item.product_name.trim() === '') &&
                    (!item.product_variant || item.product_variant.trim() === '') &&
                    (!item.product_link || item.product_link.trim() === '') &&
                    (item.quantity <= 1) &&
                    (item.price <= 0)) {
                    // Xóa lỗi cho dòng trống cuối cùng
                    const inputs = document.querySelectorAll(`[data-index="${index}"]`);
                    inputs.forEach(input => clearInputError(input));
                    return;
                }

                // Kiểm tra từng trường và hiển thị lỗi

                // Kiểm tra tên sản phẩm
                const nameInput = document.querySelector(`.product-name[data-index="${index}"]`);
                if (nameInput) {
                    if (!item.product_name || item.product_name.trim() === '') {
                        nameInput.classList.add('error-field');
                        showInputError(nameInput, 'Tên sản phẩm không được để trống');
                        isValid = false;
                    } else {
                        nameInput.classList.remove('error-field');
                        clearInputError(nameInput);
                    }
                }

                // Kiểm tra số lượng
                const quantityInput = document.querySelector(`.product-quantity[data-index="${index}"]`);
                if (quantityInput) {
                    if (!item.quantity || item.quantity <= 0) {
                        quantityInput.classList.add('error-field');
                        showInputError(quantityInput, 'Số lượng phải lớn hơn 0');
                        isValid = false;
                    } else {
                        quantityInput.classList.remove('error-field');
                        clearInputError(quantityInput);
                    }
                }

                // Kiểm tra giá
                const priceInput = document.querySelector(`.product-price[data-index="${index}"]`);
                if (priceInput) {
                    if (!item.price || item.price <= 0) {
                        priceInput.classList.add('error-field');
                        showInputError(priceInput, 'Giá phải lớn hơn 0');
                        isValid = false;
                    } else {
                        priceInput.classList.remove('error-field');
                        clearInputError(priceInput);
                    }
                }

                // Nếu sản phẩm hợp lệ, thêm vào danh sách validItems
                if (item.product_name && item.product_name.trim() !== '' &&
                    item.quantity > 0 && item.price > 0) {
                    validItems.push({
                        product_link: item.product_link || '',
                        product_name: item.product_name,
                        product_variant: item.product_variant || '',
                        quantity: parseInt(item.quantity),
                        price: parseFloat(item.price)
                    });
                }
            });

            if (!isValid) {
                return;
            }

            // Lấy nhân viên phụ trách
            const staffId = document.getElementById('staffSelect').value || currentStaffId || null;

            // Gửi yêu cầu tạo đơn mua hộ
            showLoader(true, 'Đang tạo đơn mua hộ...');

            try {
                // Nếu là nhân viên, luôn gửi staff_id là ID của nhân viên đó
                // Nếu là admin, gửi staff_id được chọn từ dropdown
                const staffIdToSend = isAdmin ? staffId : currentStaffId;

                const response = await fetch(`${API_URL}/api/admin/purchase-orders`, {
                    method: 'POST',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        user_id: userId,
                        staff_id: staffIdToSend,
                        exchange_rate: exchangeRate,
                        deposit_percentage: depositPercentage,
                        note: note,
                        items: validItems
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('Đã tạo đơn mua hộ thành công!');

                    // Xóa dữ liệu form
                    localStorage.removeItem('purchaseOrderFormData');

                    // Reset form
                    document.getElementById('userSelect').value = '';
                    document.getElementById('exchangeRate').value = '';
                    document.getElementById('depositPercentage').value = '';
                    document.getElementById('note').value = '';
                    purchaseOrderItems = [];
                    updateItemsTable();

                    // Ẩn form
                    document.getElementById('purchaseOrderForm').style.display = 'none';

                    // Nếu là admin, chuyển sang tab quản lý đơn
                    if (isAdmin) {
                        document.querySelector('.tab[data-tab="manageOrders"]').click();
                    } else {
                        // Nếu là nhân viên, tải lại danh sách đơn hàng để hiển thị đơn vừa tạo
                        loadPurchaseOrders();
                    }

                    // Khởi tạo lại Select2
                    $('#userSelect').trigger('change');
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi tạo đơn mua hộ');
                }
            } catch (error) {
                console.error('Lỗi tạo đơn mua hộ:', error);
                alert('Có lỗi xảy ra khi tạo đơn mua hộ. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Thiết lập sự kiện cho modal
        function setupModalEvents() {
            // Đóng modal khi nhấp vào nút đóng
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                const closeBtn = modal.querySelector('.close');
                closeBtn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });

                // Đóng modal khi nhấp bên ngoài modal (ngoại trừ updateItemModal và manualPaymentModal)
                if (modal.id !== 'updateItemModal' && modal.id !== 'manualPaymentModal') {
                    window.addEventListener('click', (event) => {
                        if (event.target === modal) {
                            modal.style.display = 'none';
                        }
                    });
                }
            });

            // Đóng modal khi nhấp vào nút "Hủy"
            const cancelBtns = document.querySelectorAll('.close-modal');
            cancelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const modal = btn.closest('.modal');
                    modal.style.display = 'none';
                });
            });

            // Thiết lập sự kiện cho nút thêm tracking
            document.getElementById('addTrackingBtn').addEventListener('click', () => {
                if (!currentOrderId) return;

                // Đảm bảo các modal khác đều bị ẩn trước khi hiển thị modal mới
                modals.forEach(m => {
                    m.style.display = 'none';
                });

                // Hiển thị modal thêm tracking
                document.getElementById('addTrackingModal').style.display = 'block';
            });

            // Thiết lập sự kiện cho nút lưu tracking
            document.getElementById('saveTrackingBtn').addEventListener('click', addTracking);

            // Thiết lập sự kiện cho nút kiểm tra thanh toán
            document.getElementById('checkPaymentBtn').addEventListener('click', () => {
                if (!currentOrderId) return;

                checkOrderPayment(currentOrderId);
            });

            // Thiết lập sự kiện cho nút xác nhận thanh toán thủ công
            document.getElementById('manualPaymentBtn').addEventListener('click', () => {
                if (!currentOrderId) return;

                // Đảm bảo các modal khác đều bị ẩn trước khi hiển thị modal mới
                modals.forEach(m => {
                    m.style.display = 'none';
                });

                // Đặt ngày mặc định là ngày hiện tại
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                document.getElementById('manualPaymentDate').value = formattedDate;

                // Xóa dữ liệu cũ
                document.getElementById('manualPaymentAmount').value = '';
                document.getElementById('manualPaymentNote').value = '';

                // Hiển thị modal xác nhận thanh toán thủ công
                document.getElementById('manualPaymentModal').style.display = 'block';
            });

            // Thiết lập sự kiện cho nút xác nhận thanh toán thủ công
            document.getElementById('confirmManualPaymentBtn').addEventListener('click', confirmManualPayment);

            // Thiết lập sự kiện cho nút lưu cập nhật thông tin sản phẩm
            document.getElementById('saveItemUpdateBtn').addEventListener('click', updateOrderItem);

            // Thiết lập sự kiện cho modal chỉnh sửa đơn hàng
            document.getElementById('addEditItemBtn').addEventListener('click', addEditItem);
            document.getElementById('saveEditOrderBtn').addEventListener('click', saveEditOrder);
        }

        // Tải danh sách đơn mua hộ
        async function loadPurchaseOrders() {
            // Xóa điều kiện kiểm tra isAdmin để cả admin và nhân viên đều có thể tải danh sách đơn hàng
            showLoader(true, 'Đang tải danh sách đơn hàng...');

            try {
                // Thêm cache busting để đảm bảo dữ liệu mới nhất
                const timestamp = new Date().getTime();
                const response = await fetch(`${API_URL}/api/admin/purchase-orders?_t=${timestamp}`, {
                    headers: {
                        ...getAuthHeader(),
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                if (response.ok) {
                    const orders = await response.json();
                    console.log('Dữ liệu đơn mua hộ từ API:', orders);

                    // Xử lý dữ liệu trước khi hiển thị
                    const processedOrders = Array.isArray(orders) ? orders.map(order => {
                        // Đảm bảo các thuộc tính cần thiết tồn tại
                        return {
                            ...order,
                            // Nếu order không có items, tạo mảng rỗng
                            items: order.items || []
                        };
                    }) : [];

                    // Hiển thị dữ liệu trong bảng thông thường (tab Tạo đơn mua hộ)
                    displayPurchaseOrders(processedOrders);

                    // Hiển thị dữ liệu trong bảng admin chi tiết (tab Quản lý đơn) chỉ khi người dùng là admin
                    if (isAdmin) {
                        displayAdminPurchaseOrders(processedOrders);
                    }
                } else {
                    console.error('Không thể tải danh sách đơn mua hộ');
                }
            } catch (error) {
                console.error('Lỗi tải danh sách đơn mua hộ:', error);
            } finally {
                showLoader(false);
            }
        }

        // Tải dữ liệu cho Dashboard
        async function loadDashboardData() {
            showLoader(true, 'Đang tải dữ liệu dashboard...');

            // Ẩn thông báo lỗi trước khi tải dữ liệu mới
            const errorElement = document.getElementById('dashboardErrorMessage');
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            }

            try {
                // Tải dữ liệu đơn hàng
                const response = await fetch(`${API_URL}/api/admin/purchase-orders`, {
                    headers: getAuthHeader()
                });

                if (response.ok) {
                    const orders = await response.json();
                    console.log('Dữ liệu đơn mua hộ cho Dashboard:', orders);

                    // Lọc đơn hàng theo khoảng thời gian
                    const filteredOrders = Array.isArray(orders) ? orders.filter(order => {
                        const orderDate = new Date(order.created_at);
                        return orderDate >= dashboardData.startDate && orderDate <= dashboardData.endDate;
                    }) : [];

                    // Lưu dữ liệu đã lọc
                    dashboardData.orders = filteredOrders;

                    // Xử lý và hiển thị dữ liệu
                    processDashboardDataUI();
                } else {
                    console.error('Không thể tải dữ liệu cho Dashboard');
                }
            } catch (error) {
                console.error('Lỗi tải dữ liệu cho Dashboard:', error);
                // Sử dụng hàm updateElementIfExists thay vì truy cập trực tiếp
                const errorMessage = `Lỗi tải dữ liệu cho Dashboard: ${error.message}`;
                const errorElement = document.getElementById('dashboardErrorMessage');
                if (errorElement) {
                    errorElement.textContent = errorMessage;
                    errorElement.style.display = 'block';
                }
            } finally {
                showLoader(false);
            }
        }

        // Xử lý dữ liệu cho Dashboard
        function processDashboardDataUI() {
            console.log('Xử lý dữ liệu Dashboard với', dashboardData.orders.length, 'đơn hàng');

            // Ẩn thông báo lỗi trước khi xử lý dữ liệu mới
            const errorElement = document.getElementById('dashboardErrorMessage');
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            }

            try {
                if (!dashboardData.orders || !Array.isArray(dashboardData.orders)) {
                    console.error('Không có dữ liệu đơn hàng để xử lý');
                    return;
                }

                // Sử dụng dashboard-processor.js để xử lý dữ liệu
                if (typeof window.processDashboardData === 'function') {
                    const processedData = window.processDashboardData(dashboardData.orders);

                    // Cập nhật giao diện với dữ liệu đã xử lý
                    updateDashboardUI(processedData);
                } else {
                    console.error('Hàm processDashboardData không tồn tại trong window');
                    // Fallback: Sử dụng các hàm xử lý cũ
                    calculateFinancialOverview();
                    calculateOrderStatus();
                    calculatePaymentStatus();
                    calculateStaffPerformance();
                    calculateAlerts();
                }
            } catch (error) {
                console.error('Lỗi khi xử lý dữ liệu Dashboard:', error);
                // Sử dụng hàm updateElementIfExists thay vì truy cập trực tiếp
                const errorMessage = `Lỗi xử lý dữ liệu Dashboard: ${error.message}`;
                const errorElement = document.getElementById('dashboardErrorMessage');
                if (errorElement) {
                    errorElement.textContent = errorMessage;
                    errorElement.style.display = 'block';
                }
            }
        }

        // Cập nhật giao diện Dashboard với dữ liệu đã xử lý
        function updateDashboardUI(data) {
            // Định nghĩa hàm updateElementIfExists
            const updateElementIfExists = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            };

            // Cập nhật tổng quan tài chính
            updateElementIfExists('totalActiveOrders', data.overview.totalActiveOrders);
            updateElementIfExists('totalSellUSD', `$${data.overview.totalSellUSD.toFixed(2)}`);
            updateElementIfExists('totalSellVND', formatter.format(data.overview.totalSellVND));
            updateElementIfExists('totalPurchaseUSD', `$${data.overview.totalPurchaseUSD.toFixed(2)}`);
            updateElementIfExists('totalPurchaseVND', formatter.format(data.overview.totalPurchaseVND));
            updateElementIfExists('totalGrossProfit', formatter.format(data.overview.totalGrossProfit));
            updateElementIfExists('profitMargin', data.overview.profitMargin.toFixed(2) + '%');
            updateElementIfExists('totalCollected', formatter.format(data.overview.totalCollected));
            updateElementIfExists('totalDebt', formatter.format(data.overview.totalDebt));
            updateElementIfExists('totalItemsToBuy', `Cần mua: ${data.overview.totalItemsToBuy}`);
            updateElementIfExists('totalItemsBought', `Đã mua: ${data.overview.totalItemsBought}`);
            updateElementIfExists('purchaseProgress', data.overview.purchaseProgress.toFixed(2) + '%');

            // Cập nhật trạng thái đơn hàng
            updateElementIfExists('newOrdersCount', data.orderStatus.newOrders);
            updateElementIfExists('depositedOrdersCount', data.orderStatus.depositedOrders);
            updateElementIfExists('buyingOrdersCount', data.orderStatus.buyingOrders);
            updateElementIfExists('waitingWarehouseCount', data.orderStatus.waitingWarehouse);
            updateElementIfExists('inWarehouseCount', data.orderStatus.inWarehouse);
            updateElementIfExists('shippingToVNCount', data.orderStatus.shippingToVN);
            updateElementIfExists('inVNWarehouseCount', data.orderStatus.inVNWarehouse);
            updateElementIfExists('completedOrdersCount', data.orderStatus.completedOrders);
            updateElementIfExists('cancelledOrdersCount', data.orderStatus.cancelledOrders);

            // Tạo biểu đồ trạng thái đơn hàng
            createOrderStatusChart([
                data.orderStatus.newOrders,
                data.orderStatus.depositedOrders,
                data.orderStatus.buyingOrders,
                data.orderStatus.waitingWarehouse,
                data.orderStatus.inWarehouse,
                data.orderStatus.shippingToVN,
                data.orderStatus.inVNWarehouse,
                data.orderStatus.completedOrders,
                data.orderStatus.cancelledOrders
            ]);

            // Cập nhật trạng thái thanh toán
            updateElementIfExists('unpaidOrdersCount', data.paymentStatus.unpaidOrders);
            updateElementIfExists('unpaidOrdersValue', `Tổng giá trị: ${formatter.format(data.paymentStatus.unpaidOrdersValue)}`);
            updateElementIfExists('depositedPaymentCount', data.paymentStatus.depositedOrders);
            updateElementIfExists('depositedAmount', `Tổng tiền đã cọc: ${formatter.format(data.paymentStatus.depositedAmount)}`);
            updateElementIfExists('remainingAfterDeposit', `Tổng tiền còn lại cần thu: ${formatter.format(data.paymentStatus.remainingAfterDeposit)}`);
            updateElementIfExists('paidOrdersCount', data.paymentStatus.paidOrders);
            updateElementIfExists('paidOrdersValue', `Tổng giá trị: ${formatter.format(data.paymentStatus.paidOrdersValue)}`);

            // Tạo biểu đồ trạng thái thanh toán
            createPaymentStatusChart([
                data.paymentStatus.unpaidOrders,
                data.paymentStatus.depositedOrders,
                data.paymentStatus.paidOrders
            ]);

            // Cập nhật hiệu suất nhân viên
            updateStaffPerformanceTable(data.staffPerformance);

            // Cập nhật cảnh báo
            updateAlerts(
                data.alerts.negativeGrossProfitOrders,
                data.alerts.waitingToBuyOrders,
                data.alerts.trackingNotUpdatedOrders,
                data.alerts.overduePaymentOrders
            );

            // Cập nhật tiến độ mua hàng
            updatePurchaseProgressChart(data.purchaseProgress.dayData);
        }

        // Hàm cập nhật biểu đồ tiến độ mua hàng
        function updatePurchaseProgressChart(dayData) {
            const chartElement = document.getElementById('purchaseProgressChart');
            if (!chartElement) {
                console.warn('Không tìm thấy phần tử purchaseProgressChart');
                return;
            }

            const progressCtx = chartElement.getContext('2d');

            // Hủy biểu đồ cũ nếu có
            if (dashboardData.purchaseProgressChart) {
                dashboardData.purchaseProgressChart.destroy();
            }

            // Tạo biểu đồ mới
            const dayLabels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];

            // Đảm bảo dữ liệu hợp lệ
            let validData = Array.isArray(dayData) && dayData.length === 7
                ? dayData
                : [0, 0, 0, 0, 0, 0, 0];

            // Tính tổng số lượng đã mua
            const totalPurchased = validData.reduce((sum, value) => sum + value, 0);
            console.log('Dữ liệu tiến độ mua hàng theo ngày:', validData, 'Tổng số lượng đã mua:', totalPurchased);

            dashboardData.purchaseProgressChart = new Chart(progressCtx, {
                type: 'bar',
                data: {
                    labels: dayLabels,
                    datasets: [{
                        label: 'Số lượng sản phẩm đã mua',
                        data: validData,
                        backgroundColor: '#0366d6',
                        borderColor: '#0366d6',
                        borderWidth: 1,
                        borderRadius: 4,
                        barThickness: 16
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `Số lượng: ${context.raw}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: '#f0f0f0'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Xử lý sự kiện cho các menu item trong sidebar và chart actions
        document.addEventListener('click', function(e) {
            // Xử lý click cho sidebar menu items
            if (e.target.closest('.dashboard-sidebar-menu-item')) {
                const menuItem = e.target.closest('.dashboard-sidebar-menu-item');

                // Xóa class active từ tất cả các menu item
                document.querySelectorAll('.dashboard-sidebar-menu-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Thêm class active cho menu item được chọn
                menuItem.classList.add('active');
            }

            // Xử lý click cho chart actions
            if (e.target.classList.contains('chart-action') && !e.target.classList.contains('active')) {
                const parent = e.target.closest('.chart-actions');
                if (parent) {
                    // Xóa class active từ tất cả các chart action trong cùng nhóm
                    parent.querySelectorAll('.chart-action').forEach(action => {
                        action.classList.remove('active');
                    });

                    // Thêm class active cho chart action được chọn
                    e.target.classList.add('active');
                }
            }
        });

        // Tính toán tổng quan tài chính
        function calculateFinancialOverview() {
            const orders = dashboardData.orders;

            // Tổng số đơn hàng đang hoạt động (không bao gồm đơn đã hủy)
            const activeOrders = orders.filter(order => order.status !== 'cancelled');
            document.getElementById('totalActiveOrders').textContent = activeOrders.length;

            // Tính toán tổng doanh thu (giá trị bán ra)
            let totalSellUSD = 0;
            let totalSellVND = 0;

            // Tính toán tổng chi phí (giá trị mua vào)
            let totalPurchaseUSD = 0;
            let totalPurchaseVND = 0;

            // Tính toán tổng lợi nhuận gộp
            let totalGrossProfit = 0;

            // Tính toán tổng tiền đã thanh toán
            let totalDeposited = 0;
            let totalPaid = 0;

            // Tính toán tổng tiền còn nợ
            let totalDebt = 0;

            // Tính toán tiến độ mua hàng
            let totalItemsToBuy = 0;
            let totalItemsBought = 0;

            // Duyệt qua từng đơn hàng
            orders.forEach(order => {
                // Tính tổng doanh thu
                const orderTotalUSD = parseFloat(order.total_amount) || 0;
                const orderExchangeRate = parseFloat(order.exchange_rate) || 0;
                totalSellUSD += orderTotalUSD;
                totalSellVND += orderTotalUSD * orderExchangeRate;

                // Tính tổng chi phí mua vào
                let orderPurchaseUSD = 0;
                let orderPurchaseVND = 0;

                // Duyệt qua từng sản phẩm trong đơn hàng
                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const purchasedQty = parseFloat(item.purchased_quantity) || 0;
                        const purchasePrice = parseFloat(item.purchase_price) || 0;
                        const purchaseExchangeRate = parseFloat(item.purchase_exchange_rate) || 0;

                        if (purchasedQty > 0 && purchasePrice > 0) {
                            orderPurchaseUSD += purchasePrice * purchasedQty;

                            if (purchaseExchangeRate > 0) {
                                orderPurchaseVND += purchasePrice * purchasedQty * purchaseExchangeRate;
                            }
                        }

                        // Tính tiến độ mua hàng
                        const quantity = parseFloat(item.quantity) || 0;
                        totalItemsToBuy += quantity;
                        totalItemsBought += purchasedQty;
                    });
                }

                totalPurchaseUSD += orderPurchaseUSD;
                totalPurchaseVND += orderPurchaseVND;

                // Tính lợi nhuận gộp
                const orderGrossProfit = (orderTotalUSD * orderExchangeRate) - orderPurchaseVND;
                totalGrossProfit += orderGrossProfit;

                // Tính tiền đã thanh toán
                const paidAmount = parseFloat(order.paid_amount) || 0;
                const totalOrderAmount = orderTotalUSD * orderExchangeRate;

                if (order.payment_status === 'deposited') {
                    totalDeposited += paidAmount;
                } else if (order.payment_status === 'completed' || order.payment_status === 'paid') {
                    totalPaid += paidAmount;
                }

                // Tính tiền còn nợ
                totalDebt += totalOrderAmount - paidAmount;
            });

            // Cập nhật giao diện
            document.getElementById('totalSellUSD').textContent = `$${totalSellUSD.toFixed(2)}`;
            document.getElementById('totalSellVND').textContent = formatter.format(totalSellVND);

            document.getElementById('totalPurchaseUSD').textContent = `$${totalPurchaseUSD.toFixed(2)}`;
            document.getElementById('totalPurchaseVND').textContent = formatter.format(totalPurchaseVND);

            document.getElementById('totalGrossProfit').textContent = formatter.format(totalGrossProfit);

            // Tính tỷ suất lợi nhuận gộp
            const profitMargin = totalSellVND > 0 ? (totalGrossProfit / totalSellVND) * 100 : 0;
            document.getElementById('profitMargin').textContent = `Tỷ suất: ${profitMargin.toFixed(2)}%`;

            // Cập nhật thông tin thanh toán
            document.getElementById('totalDeposited').textContent = `Đã cọc: ${formatter.format(totalDeposited)}`;
            document.getElementById('totalPaid').textContent = `Đã thanh toán đủ: ${formatter.format(totalPaid)}`;
            document.getElementById('totalCollected').textContent = `Tổng cộng đã thu: ${formatter.format(totalDeposited + totalPaid)}`;

            document.getElementById('totalDebt').textContent = formatter.format(totalDebt);

            // Cập nhật tiến độ mua hàng
            document.getElementById('totalItemsToBuy').textContent = `Tổng số lượng mục cần mua: ${totalItemsToBuy}`;
            document.getElementById('totalItemsBought').textContent = `Tổng số lượng mục đã mua: ${totalItemsBought}`;

            // Cập nhật thanh tiến độ
            const purchaseProgress = totalItemsToBuy > 0 ? (totalItemsBought / totalItemsToBuy) * 100 : 0;
            const progressBar = document.getElementById('purchaseProgressBar');
            progressBar.style.width = `${purchaseProgress}%`;
            progressBar.textContent = `${purchaseProgress.toFixed(2)}%`;
        }

        // Tính toán trạng thái đơn hàng
        function calculateOrderStatus() {
            const orders = dashboardData.orders;

            // Đếm số lượng đơn hàng theo từng trạng thái
            let newOrdersCount = 0;           // Đơn mới / Chờ xử lý ban đầu
            let depositedOrdersCount = 0;     // Đã cọc / Chờ mua hàng
            let buyingOrdersCount = 0;        // Đang mua hàng / Mua chưa hoàn tất
            let waitingWarehouseCount = 0;    // Chờ nhận hàng (về kho trung gian)
            let inWarehouseCount = 0;         // Đã nhập kho (kho trung gian)
            let shippingToVNCount = 0;        // Đang về VN
            let inVNWarehouseCount = 0;       // Đã nhập kho VN
            let completedOrdersCount = 0;     // Hoàn thành
            let cancelledOrdersCount = 0;     // Đã hủy / Có vấn đề

            // Duyệt qua từng đơn hàng
            orders.forEach(order => {
                // Kiểm tra trạng thái đơn hàng
                const status = order.status || '';
                const paymentStatus = order.payment_status || '';

                // Kiểm tra tiến độ mua hàng
                let allItemsPurchased = true;
                let anyItemPurchased = false;

                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const quantity = parseFloat(item.quantity) || 0;
                        const purchasedQty = parseFloat(item.purchased_quantity) || 0;

                        if (purchasedQty < quantity) {
                            allItemsPurchased = false;
                        }

                        if (purchasedQty > 0) {
                            anyItemPurchased = true;
                        }
                    });
                }

                // Phân loại đơn hàng theo trạng thái
                if (status === 'cancelled') {
                    cancelledOrdersCount++;
                } else if (status === 'hoanthanh' && (paymentStatus === 'completed' || paymentStatus === 'paid')) {
                    completedOrdersCount++;
                } else if (status === 'nhapkhovn') {
                    inVNWarehouseCount++;
                } else if (status === 'dangvevn') {
                    shippingToVNCount++;
                } else if (status === 'nhapkho') {
                    inWarehouseCount++;
                } else if (status === 'chonhanhang') {
                    waitingWarehouseCount++;
                } else if (anyItemPurchased && !allItemsPurchased) {
                    buyingOrdersCount++;
                } else if (paymentStatus === 'deposited' && !anyItemPurchased) {
                    depositedOrdersCount++;
                } else {
                    newOrdersCount++;
                }
            });

            // Cập nhật giao diện
            document.getElementById('newOrdersCount').textContent = newOrdersCount;
            document.getElementById('depositedOrdersCount').textContent = depositedOrdersCount;
            document.getElementById('buyingOrdersCount').textContent = buyingOrdersCount;
            document.getElementById('completedOrdersCount').textContent = completedOrdersCount;
            document.getElementById('cancelledOrdersCount').textContent = cancelledOrdersCount;

            // Cập nhật các phần tử khác nếu tồn tại
            const updateElementIfExists = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            };

            updateElementIfExists('waitingWarehouseCount', waitingWarehouseCount);
            updateElementIfExists('inWarehouseCount', inWarehouseCount);
            updateElementIfExists('shippingToVNCount', shippingToVNCount);
            updateElementIfExists('inVNWarehouseCount', inVNWarehouseCount);

            // Tạo biểu đồ trạng thái đơn hàng
            createOrderStatusChart([
                newOrdersCount,
                depositedOrdersCount,
                buyingOrdersCount,
                waitingWarehouseCount,
                inWarehouseCount,
                shippingToVNCount,
                inVNWarehouseCount,
                completedOrdersCount,
                cancelledOrdersCount
            ]);
        }

        // Tạo biểu đồ trạng thái đơn hàng
        function createOrderStatusChart(data) {
            const chartElement = document.getElementById('orderStatusChart');
            if (!chartElement) {
                console.warn('Không tìm thấy phần tử orderStatusChart');
                return;
            }

            const ctx = chartElement.getContext('2d');

            // Hủy biểu đồ cũ nếu có
            if (dashboardData.orderStatusChart) {
                dashboardData.orderStatusChart.destroy();
            }

            // Tạo biểu đồ mới
            dashboardData.orderStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [
                        'Đơn mới / Chờ xử lý',
                        'Đã cọc / Chờ mua hàng',
                        'Đang mua hàng',
                        'Chờ nhận hàng',
                        'Đã nhập kho',
                        'Đang về VN',
                        'Đã nhập kho VN',
                        'Hoàn thành',
                        'Đã hủy / Có vấn đề'
                    ],
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#f97316', // Cam - Đơn mới
                            '#0366d6', // Xanh dương - Đã cọc
                            '#8250df', // Tím - Đang mua hàng
                            '#ffcc00', // Vàng - Chờ nhận hàng
                            '#2da44e', // Xanh lá - Đã nhập kho
                            '#00c7be', // Xanh ngọc - Đang về VN
                            '#af52de', // Tím nhạt - Đã nhập kho VN
                            '#2da44e', // Xanh lá đậm - Hoàn thành
                            '#cf222e'  // Đỏ - Đã hủy
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 4,
                        hoverBorderColor: '#ffffff',
                        hoverOffset: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 12,
                                font: {
                                    size: 12,
                                    family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                },
                                color: '#24292e',
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        title: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#24292e',
                            bodyColor: '#24292e',
                            bodyFont: {
                                size: 13,
                                family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            },
                            titleFont: {
                                size: 14,
                                weight: 'bold',
                                family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            },
                            borderColor: '#d0d7de',
                            borderWidth: 1,
                            padding: 10,
                            boxPadding: 4,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} đơn (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '70%',
                    animation: {
                        animateScale: true,
                        animateRotate: true,
                        duration: 800,
                        easing: 'easeOutQuart'
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const clickedIndex = elements[0].index;
                            const statusLabel = this.data.labels[clickedIndex];

                            // Lọc đơn hàng theo trạng thái đã chọn
                            filterOrdersByStatus(statusLabel);
                        }
                    }
                }
            });

            // Tạo biểu đồ tiến độ mua hàng với dữ liệu thực
            if (!dashboardData.purchaseProgressChart) {
                const progressCtx = document.getElementById('purchaseProgressChart').getContext('2d');

                // Tính toán dữ liệu tiến độ mua hàng theo ngày trong tuần
                const dayLabels = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
                const dayData = [0, 0, 0, 0, 0, 0, 0]; // Khởi tạo mảng dữ liệu cho 7 ngày

                // Duyệt qua các đơn hàng để tính số lượng mục đã mua theo ngày
                dashboardData.orders.forEach(order => {
                    if (order.items && Array.isArray(order.items)) {
                        order.items.forEach(item => {
                            // Nếu có thông tin ngày mua
                            if (item.purchased_at) {
                                const purchaseDate = new Date(item.purchased_at);
                                const dayOfWeek = purchaseDate.getDay(); // 0 = CN, 1 = T2, ..., 6 = T7
                                const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Chuyển đổi sang index 0 = T2, ..., 6 = CN
                                const purchasedQty = parseFloat(item.purchased_quantity) || 0;

                                // Cộng dồn số lượng đã mua vào ngày tương ứng
                                dayData[dayIndex] += purchasedQty;
                            }
                        });
                    }
                });

                const progressData = {
                    labels: dayLabels,
                    datasets: [{
                        label: 'Mục đã mua',
                        data: dayData,
                        backgroundColor: '#0366d6',
                        borderColor: '#0366d6',
                        borderWidth: 2,
                        borderRadius: 4,
                        barThickness: 12,
                    }]
                };

                dashboardData.purchaseProgressChart = new Chart(progressCtx, {
                    type: 'bar',
                    data: progressData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: '#f0f0f5',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 12,
                                        family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                    },
                                    color: '#57606a'
                                }
                            },
                            x: {
                                grid: {
                                    display: false,
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 12,
                                        family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                    },
                                    color: '#57606a'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                titleColor: '#24292e',
                                bodyColor: '#24292e',
                                bodyFont: {
                                    size: 13,
                                    family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                },
                                titleFont: {
                                    size: 14,
                                    weight: 'bold',
                                    family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                },
                                borderColor: '#d0d7de',
                                borderWidth: 1,
                                padding: 10,
                                boxPadding: 4,
                                usePointStyle: true
                            }
                        }
                    }
                });
            }
        }

        // Tính toán trạng thái thanh toán
        function calculatePaymentStatus() {
            const orders = dashboardData.orders;

            // Đếm số lượng đơn hàng theo từng trạng thái thanh toán
            let unpaidOrdersCount = 0;     // Chưa thanh toán (unpaid, pending)
            let depositedOrdersCount = 0;  // Đã cọc (deposited)
            let paidOrdersCount = 0;       // Đã thanh toán đủ (completed, paid)

            // Tính tổng giá trị theo từng trạng thái thanh toán
            let unpaidOrdersValue = 0;
            let depositedAmount = 0;
            let remainingAfterDeposit = 0;
            let paidOrdersValue = 0;

            // Duyệt qua từng đơn hàng
            orders.forEach(order => {
                const paymentStatus = order.payment_status || '';
                const orderTotalUSD = parseFloat(order.total_amount) || 0;
                const orderExchangeRate = parseFloat(order.exchange_rate) || 0;
                const totalOrderAmount = orderTotalUSD * orderExchangeRate;
                const paidAmount = parseFloat(order.paid_amount) || 0;

                // Phân loại đơn hàng theo trạng thái thanh toán
                if (paymentStatus === 'unpaid' || paymentStatus === 'pending') {
                    unpaidOrdersCount++;
                    unpaidOrdersValue += totalOrderAmount;
                } else if (paymentStatus === 'deposited') {
                    depositedOrdersCount++;
                    depositedAmount += paidAmount;
                    remainingAfterDeposit += (totalOrderAmount - paidAmount);
                } else if (paymentStatus === 'completed' || paymentStatus === 'paid') {
                    paidOrdersCount++;
                    paidOrdersValue += totalOrderAmount;
                }
            });

            // Cập nhật giao diện
            document.getElementById('unpaidOrdersCount').textContent = unpaidOrdersCount;

            // Sử dụng hàm updateElementIfExists để cập nhật các phần tử có thể không tồn tại
            const updateElementIfExists = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            };

            updateElementIfExists('unpaidOrdersValue', `Tổng giá trị: ${formatter.format(unpaidOrdersValue)}`);
            document.getElementById('depositedPaymentCount').textContent = depositedOrdersCount;
            updateElementIfExists('depositedAmount', `Tổng tiền đã cọc: ${formatter.format(depositedAmount)}`);
            updateElementIfExists('remainingAfterDeposit', `Tổng tiền còn lại cần thu: ${formatter.format(remainingAfterDeposit)}`);
            document.getElementById('paidOrdersCount').textContent = paidOrdersCount;
            updateElementIfExists('paidOrdersValue', `Tổng giá trị: ${formatter.format(paidOrdersValue)}`);

            // Tạo biểu đồ trạng thái thanh toán
            createPaymentStatusChart([unpaidOrdersCount, depositedOrdersCount, paidOrdersCount]);
        }

        // Tạo biểu đồ trạng thái thanh toán
        function createPaymentStatusChart(data) {
            const chartElement = document.getElementById('paymentStatusChart');
            if (!chartElement) {
                console.warn('Không tìm thấy phần tử paymentStatusChart');
                return;
            }

            const ctx = chartElement.getContext('2d');

            // Hủy biểu đồ cũ nếu có
            if (dashboardData.paymentStatusChart) {
                dashboardData.paymentStatusChart.destroy();
            }

            // Tạo biểu đồ mới
            dashboardData.paymentStatusChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: [
                        'Chưa thanh toán',
                        'Đã cọc',
                        'Đã thanh toán đủ'
                    ],
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#f97316', // Cam - Chưa thanh toán
                            '#0366d6', // Xanh dương - Đã cọc
                            '#2da44e'  // Xanh lá - Đã thanh toán đủ
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 4,
                        hoverBorderColor: '#ffffff',
                        hoverOffset: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 12,
                                font: {
                                    size: 12,
                                    family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                                },
                                color: '#24292e',
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        title: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#24292e',
                            bodyColor: '#24292e',
                            bodyFont: {
                                size: 13,
                                family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            },
                            titleFont: {
                                size: 14,
                                weight: 'bold',
                                family: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            },
                            borderColor: '#d0d7de',
                            borderWidth: 1,
                            padding: 10,
                            boxPadding: 4,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} đơn (${percentage}%)`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true,
                        duration: 800,
                        easing: 'easeOutQuart'
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const clickedIndex = elements[0].index;
                            const paymentStatusLabel = this.data.labels[clickedIndex];

                            // Lọc đơn hàng theo trạng thái thanh toán đã chọn
                            filterOrdersByPaymentStatus(paymentStatusLabel);
                        }
                    }
                }
            });

            // Cập nhật các ID phụ cho bảng
            const updateElementIfExists = (targetId, sourceId) => {
                const targetElement = document.getElementById(targetId);
                const sourceElement = document.getElementById(sourceId);
                if (targetElement && sourceElement) {
                    targetElement.textContent = sourceElement.textContent;
                }
            };

            updateElementIfExists('unpaidOrdersCount2', 'unpaidOrdersCount');
            updateElementIfExists('depositedPaymentCount2', 'depositedPaymentCount');
            updateElementIfExists('paidOrdersCount2', 'paidOrdersCount');

            // Cập nhật tỷ lệ phần trăm trong bảng trạng thái đơn hàng
            updateOrderStatusPercentages();
        }

        // Lọc đơn hàng theo trạng thái
        function filterOrdersByStatus(statusLabel) {
            if (!dashboardData.orders || !Array.isArray(dashboardData.orders)) {
                console.error('Không có dữ liệu đơn hàng để lọc');
                return;
            }

            // Xác định trạng thái tương ứng từ nhãn
            let status = '';
            let paymentStatus = '';
            let needCheckItems = false;

            switch (statusLabel) {
                case 'Đơn mới / Chờ xử lý':
                    status = 'pending';
                    break;
                case 'Đã cọc / Chờ mua hàng':
                    paymentStatus = 'deposited';
                    needCheckItems = true; // Cần kiểm tra thêm là chưa có sản phẩm nào được mua
                    break;
                case 'Đang mua hàng':
                    needCheckItems = true; // Cần kiểm tra đã mua một phần nhưng chưa đủ
                    break;
                case 'Chờ nhận hàng':
                    status = 'chonhanhang';
                    break;
                case 'Đã nhập kho':
                    status = 'nhapkho';
                    break;
                case 'Đang về VN':
                    status = 'dangvevn';
                    break;
                case 'Đã nhập kho VN':
                    status = 'nhapkhovn';
                    break;
                case 'Hoàn thành':
                    status = 'hoanthanh';
                    break;
                case 'Đã hủy / Có vấn đề':
                    status = 'cancelled';
                    break;
                default:
                    console.warn('Trạng thái không xác định:', statusLabel);
                    return;
            }

            // Lọc đơn hàng theo trạng thái
            let filteredOrders = dashboardData.orders.filter(order => {
                if (status && order.status === status) return true;
                if (paymentStatus && order.payment_status === paymentStatus) {
                    // Nếu cần kiểm tra thêm về sản phẩm
                    if (needCheckItems) {
                        // Kiểm tra tiến độ mua hàng
                        let allItemsPurchased = true;
                        let anyItemPurchased = false;

                        if (order.items && Array.isArray(order.items)) {
                            order.items.forEach(item => {
                                const quantity = parseFloat(item.quantity) || 0;
                                const purchasedQty = parseFloat(item.purchased_quantity) || 0;

                                if (purchasedQty < quantity) {
                                    allItemsPurchased = false;
                                }

                                if (purchasedQty > 0) {
                                    anyItemPurchased = true;
                                }
                            });
                        }

                        // Đã cọc / Chờ mua hàng: đã cọc nhưng chưa mua sản phẩm nào
                        if (paymentStatus === 'deposited' && !anyItemPurchased) {
                            return true;
                        }

                        // Đang mua hàng: đã mua một phần nhưng chưa đủ
                        if (statusLabel === 'Đang mua hàng' && anyItemPurchased && !allItemsPurchased) {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
                return false;
            });

            // Hiển thị kết quả lọc
            showFilteredOrders(filteredOrders, `Đơn hàng theo trạng thái: ${statusLabel}`);
        }

        // Lọc đơn hàng theo trạng thái thanh toán
        function filterOrdersByPaymentStatus(paymentStatusLabel) {
            if (!dashboardData.orders || !Array.isArray(dashboardData.orders)) {
                console.error('Không có dữ liệu đơn hàng để lọc');
                return;
            }

            // Xác định trạng thái thanh toán tương ứng từ nhãn
            let paymentStatus = '';

            switch (paymentStatusLabel) {
                case 'Chưa thanh toán':
                    paymentStatus = 'unpaid';
                    break;
                case 'Đã cọc':
                    paymentStatus = 'deposited';
                    break;
                case 'Đã thanh toán đủ':
                    paymentStatus = 'paid';
                    break;
                default:
                    console.warn('Trạng thái thanh toán không xác định:', paymentStatusLabel);
                    return;
            }

            // Lọc đơn hàng theo trạng thái thanh toán
            let filteredOrders = dashboardData.orders.filter(order => {
                if (paymentStatus === 'unpaid' && (order.payment_status === 'unpaid' || order.payment_status === 'pending')) {
                    return true;
                }
                if (paymentStatus === 'paid' && (order.payment_status === 'paid' || order.payment_status === 'completed')) {
                    return true;
                }
                return order.payment_status === paymentStatus;
            });

            // Hiển thị kết quả lọc
            showFilteredOrders(filteredOrders, `Đơn hàng theo trạng thái thanh toán: ${paymentStatusLabel}`);
        }

        // Hiển thị danh sách đơn hàng đã lọc
        function showFilteredOrders(orders, title) {
            // Chuyển sang tab Quản lý đơn
            const manageOrdersTab = document.querySelector('.tab[data-tab="manageOrders"]');
            if (manageOrdersTab) {
                // Kích hoạt tab Quản lý đơn
                document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                manageOrdersTab.classList.add('active');

                // Hiển thị nội dung tab Quản lý đơn
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                document.getElementById('manageOrdersTab').classList.add('active');

                // Hiển thị tiêu đề lọc
                const filterTitle = document.createElement('div');
                filterTitle.className = 'filter-title';
                filterTitle.style.margin = '10px 0';
                filterTitle.style.padding = '10px';
                filterTitle.style.backgroundColor = '#f0f6fc';
                filterTitle.style.borderRadius = '6px';
                filterTitle.style.fontWeight = 'bold';
                filterTitle.style.display = 'flex';
                filterTitle.style.justifyContent = 'space-between';
                filterTitle.style.alignItems = 'center';

                filterTitle.innerHTML = `
                    <span>${title} (${orders.length} đơn hàng)</span>
                    <button id="clearFilterBtn" style="padding: 5px 10px; background-color: #f5f5f7; border: 1px solid #d2d2d7; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-times"></i> Xóa bộ lọc
                    </button>
                `;

                // Xóa tiêu đề lọc cũ nếu có
                const oldFilterTitle = document.querySelector('.filter-title');
                if (oldFilterTitle) {
                    oldFilterTitle.remove();
                }

                // Thêm tiêu đề lọc vào đầu phần nội dung tab Quản lý đơn
                const manageOrdersTabContent = document.getElementById('manageOrdersTab');
                manageOrdersTabContent.insertBefore(filterTitle, manageOrdersTabContent.firstChild);

                // Thêm sự kiện cho nút xóa bộ lọc
                document.getElementById('clearFilterBtn').addEventListener('click', function() {
                    // Xóa tiêu đề lọc
                    filterTitle.remove();

                    // Tải lại tất cả đơn hàng
                    loadPurchaseOrders();
                });

                // Hiển thị danh sách đơn hàng đã lọc
                if (isAdmin) {
                    displayAdminPurchaseOrders(orders);
                } else {
                    displayPurchaseOrders(orders);
                }
            }
        }

        // Cập nhật tỷ lệ phần trăm trong bảng trạng thái đơn hàng
        function updateOrderStatusPercentages() {
            // Cập nhật ID phụ cho bảng trạng thái đơn hàng nếu tồn tại
            const updateElementIfExists = (targetId, sourceId) => {
                const targetElement = document.getElementById(targetId);
                const sourceElement = document.getElementById(sourceId);
                if (targetElement && sourceElement) {
                    targetElement.textContent = sourceElement.textContent;
                }
            };

            updateElementIfExists('newOrdersCount2', 'newOrdersCount');
            updateElementIfExists('depositedOrdersCount2', 'depositedOrdersCount');
            updateElementIfExists('buyingOrdersCount2', 'buyingOrdersCount');
            updateElementIfExists('completedOrdersCount2', 'completedOrdersCount');
            updateElementIfExists('cancelledOrdersCount2', 'cancelledOrdersCount');

            // Tính tổng số đơn hàng
            const totalActiveOrdersElement = document.getElementById('totalActiveOrders');
            if (!totalActiveOrdersElement) return;

            const totalOrders = parseInt(totalActiveOrdersElement.textContent) || 0;

            if (totalOrders > 0) {
                // Cập nhật tỷ lệ phần trăm cho từng trạng thái
                const tableRows = document.querySelectorAll('.dashboard-table tbody tr');
                tableRows.forEach(row => {
                    const countCell = row.cells[1];
                    const percentCell = row.cells[2];
                    if (countCell && percentCell && percentCell.textContent === '0%') {
                        const count = parseInt(countCell.textContent) || 0;
                        const percentage = Math.round((count / totalOrders) * 100);
                        percentCell.textContent = `${percentage}%`;
                    }
                });
            }
        }

        // Tính toán hiệu suất nhân viên
        function calculateStaffPerformance() {
            const orders = dashboardData.orders;

            console.log('Tính toán hiệu suất nhân viên với', orders.length, 'đơn hàng');

            // Tạo đối tượng để lưu trữ dữ liệu hiệu suất nhân viên
            const staffPerformance = {};

            // Nếu không có đơn hàng, thêm dữ liệu mẫu để kiểm tra giao diện
            if (!orders || orders.length === 0) {
                console.log('Không có dữ liệu đơn hàng cho hiệu suất nhân viên');

                // Thêm một mẫu dữ liệu để kiểm tra giao diện
                staffPerformance['sample'] = {
                    name: 'Dữ liệu mẫu',
                    totalOrders: 0,
                    completedOrders: 0,
                    processingOrders: 0,
                    grossProfit: 0,
                    onTimeCompletionRate: 0
                };

                updateStaffPerformanceTable(staffPerformance);
                return;
            }

            // Duyệt qua từng đơn hàng
            orders.forEach(order => {
                // Kiểm tra xem đơn hàng có thông tin nhân viên không
                const staffId = order.staff_id || order.admin_id || 'unknown';
                const staffName = order.staff_name || order.admin_name || 'Không xác định';

                console.log('Xử lý đơn hàng:', order.code, 'Nhân viên:', staffName, 'ID:', staffId);

                const status = order.status || '';
                const orderTotalUSD = parseFloat(order.total_amount) || 0;
                const orderExchangeRate = parseFloat(order.exchange_rate) || 0;

                // Tính tổng chi phí mua vào
                let orderPurchaseVND = 0;

                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const purchasedQty = parseFloat(item.purchased_quantity) || 0;
                        const purchasePrice = parseFloat(item.purchase_price) || 0;
                        const purchaseExchangeRate = parseFloat(item.purchase_exchange_rate) || 0;

                        if (purchasedQty > 0 && purchasePrice > 0 && purchaseExchangeRate > 0) {
                            orderPurchaseVND += purchasePrice * purchasedQty * purchaseExchangeRate;
                        }
                    });
                }

                // Tính lợi nhuận gộp
                const orderGrossProfit = (orderTotalUSD * orderExchangeRate) - orderPurchaseVND;

                // Kiểm tra xem nhân viên đã có trong đối tượng hiệu suất chưa
                if (!staffPerformance[staffId]) {
                    staffPerformance[staffId] = {
                        name: staffName,
                        totalOrders: 0,
                        completedOrders: 0,
                        processingOrders: 0,
                        grossProfit: 0,
                        onTimeCompletionRate: 0
                    };
                }

                // Cập nhật dữ liệu hiệu suất nhân viên
                staffPerformance[staffId].totalOrders++;

                if (status === 'hoanthanh') {
                    staffPerformance[staffId].completedOrders++;
                } else if (status !== 'cancelled') {
                    staffPerformance[staffId].processingOrders++;
                }

                staffPerformance[staffId].grossProfit += orderGrossProfit;

                // Tính tỷ lệ hoàn thành đúng hạn (giả định 100% vì không có dữ liệu về thời hạn)
                staffPerformance[staffId].onTimeCompletionRate = 100;
            });

            console.log('Kết quả hiệu suất nhân viên:', staffPerformance);

            // Cập nhật bảng hiệu suất nhân viên
            updateStaffPerformanceTable(staffPerformance);
        }

        // Cập nhật bảng hiệu suất nhân viên
        function updateStaffPerformanceTable(staffPerformance) {
            console.log('Cập nhật bảng hiệu suất nhân viên với dữ liệu:', staffPerformance);

            const tableBody = document.querySelector('#staffPerformanceTable tbody');
            if (!tableBody) {
                console.error('Không tìm thấy phần tử #staffPerformanceTable tbody');
                return;
            }

            tableBody.innerHTML = '';

            // Kiểm tra xem có dữ liệu không
            if (!staffPerformance || Object.keys(staffPerformance).length === 0) {
                console.log('Không có dữ liệu hiệu suất nhân viên');
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="6" style="text-align: center;">Không có dữ liệu hiệu suất nhân viên</td>';
                tableBody.appendChild(row);
                return;
            }

            // Duyệt qua từng nhân viên
            Object.values(staffPerformance).forEach(staff => {
                const row = document.createElement('tr');

                // Tạo các ô dữ liệu
                row.innerHTML = `
                    <td>${staff.name}</td>
                    <td>${staff.totalOrders}</td>
                    <td>${staff.completedOrders}</td>
                    <td>${staff.processingOrders}</td>
                    <td>${formatter.format(staff.grossProfit)}</td>
                    <td>${staff.onTimeCompletionRate}%</td>
                `;

                // Thêm hàng vào bảng
                tableBody.appendChild(row);
            });
        }

        // Tính toán cảnh báo
        function calculateAlerts() {
            const orders = dashboardData.orders;

            // Đơn hàng có lợi nhuận âm
            const negativeGrossProfitOrders = [];

            // Đơn hàng chờ mua quá 2 ngày
            const waitingToBuyOrders = [];

            // Đơn hàng có tracking chưa cập nhật
            const trackingNotUpdatedOrders = [];

            // Đơn hàng quá hạn thanh toán
            const overduePaymentOrders = [];

            // Duyệt qua từng đơn hàng
            orders.forEach(order => {
                const orderCode = order.code || '';
                const staffName = order.staff_name || order.admin_name || 'Không xác định';
                const status = order.status || '';
                const paymentStatus = order.payment_status || '';
                const orderTotalUSD = parseFloat(order.total_amount) || 0;
                const orderExchangeRate = parseFloat(order.exchange_rate) || 0;
                const depositDate = order.deposit_date ? new Date(order.deposit_date) : null;
                const vnWarehouseDate = order.vn_warehouse_date ? new Date(order.vn_warehouse_date) : null;
                const lastPurchaseUpdateDate = order.last_purchase_update_date ? new Date(order.last_purchase_update_date) : null;
                const createdAt = order.created_at ? new Date(order.created_at) : null;
                const paidAmount = parseFloat(order.paid_amount) || 0;
                const totalOrderAmount = orderTotalUSD * orderExchangeRate;

                // Tính tổng chi phí mua vào
                let orderPurchaseVND = 0;

                // Kiểm tra tiến độ mua hàng
                let allItemsPurchased = true;
                let anyItemPurchased = false;

                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const purchasedQty = parseFloat(item.purchased_quantity) || 0;
                        const purchasePrice = parseFloat(item.purchase_price) || 0;
                        const purchaseExchangeRate = parseFloat(item.purchase_exchange_rate) || 0;
                        const quantity = parseFloat(item.quantity) || 0;

                        if (purchasedQty > 0 && purchasePrice > 0 && purchaseExchangeRate > 0) {
                            orderPurchaseVND += purchasePrice * purchasedQty * purchaseExchangeRate;
                        }

                        if (purchasedQty < quantity) {
                            allItemsPurchased = false;
                        }

                        if (purchasedQty > 0) {
                            anyItemPurchased = true;
                        }
                    });
                }

                // Tính lợi nhuận gộp
                const orderGrossProfit = (orderTotalUSD * orderExchangeRate) - orderPurchaseVND;

                // Kiểm tra đơn hàng có lợi nhuận âm
                if (orderGrossProfit < 0) {
                    negativeGrossProfitOrders.push({
                        code: orderCode,
                        grossProfit: orderGrossProfit,
                        staffName: staffName
                    });
                }

                // Kiểm tra đơn hàng chờ mua quá 2 ngày
                if (paymentStatus === 'deposited' && !anyItemPurchased && depositDate) {
                    const today = new Date();
                    const daysSinceDeposit = Math.floor((today - depositDate) / (1000 * 60 * 60 * 24));

                    if (daysSinceDeposit > 2) {
                        waitingToBuyOrders.push({
                            code: orderCode,
                            depositDate: depositDate,
                            daysSinceDeposit: daysSinceDeposit,
                            staffName: staffName
                        });
                    }
                }

                // Kiểm tra đơn hàng có tracking chưa cập nhật
                if (anyItemPurchased && lastPurchaseUpdateDate) {
                    const today = new Date();
                    const daysSinceLastUpdate = Math.floor((today - lastPurchaseUpdateDate) / (1000 * 60 * 60 * 24));

                    if (daysSinceLastUpdate > 3 && !order.tracking_number) {
                        trackingNotUpdatedOrders.push({
                            code: orderCode,
                            lastUpdateDate: lastPurchaseUpdateDate,
                            daysSinceLastUpdate: daysSinceLastUpdate,
                            staffName: staffName
                        });
                    }
                }

                // Kiểm tra đơn hàng quá hạn thanh toán
                if (status === 'nhapkhovn' && paymentStatus !== 'completed' && paymentStatus !== 'paid' && vnWarehouseDate) {
                    const today = new Date();
                    const daysSinceVNWarehouse = Math.floor((today - vnWarehouseDate) / (1000 * 60 * 60 * 24));

                    if (daysSinceVNWarehouse > 2) {
                        overduePaymentOrders.push({
                            code: orderCode,
                            vnWarehouseDate: vnWarehouseDate,
                            daysSinceVNWarehouse: daysSinceVNWarehouse,
                            staffName: staffName,
                            remainingAmount: totalOrderAmount - paidAmount
                        });
                    }
                }
            });

            // Cập nhật giao diện cảnh báo
            updateAlerts(negativeGrossProfitOrders, waitingToBuyOrders, trackingNotUpdatedOrders, overduePaymentOrders);
        }

        // Cập nhật giao diện cảnh báo
        function updateAlerts(negativeGrossProfitOrders, waitingToBuyOrders, trackingNotUpdatedOrders, overduePaymentOrders) {
            // Định nghĩa hàm updateElementIfExists
            const updateElementIfExists = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            };

            // Cập nhật đơn hàng có lợi nhuận âm
            updateElementIfExists('negativeGrossProfitCount', `${negativeGrossProfitOrders.length} đơn`);
            const negativeGrossProfitList = document.getElementById('negativeGrossProfitList');
            if (!negativeGrossProfitList) {
                console.warn('Không tìm thấy phần tử negativeGrossProfitList');
                return;
            }
            negativeGrossProfitList.innerHTML = '';

            negativeGrossProfitOrders.forEach(order => {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = `
                    <strong>${order.code}</strong>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span><i class="fas fa-user-tie"></i> ${order.staffName}</span>
                        <span style="color: #ff3b30; font-weight: 600;"><i class="fas fa-chart-line"></i> ${formatter.format(order.grossProfit)}</span>
                    </div>
                `;
                negativeGrossProfitList.appendChild(alertItem);
            });

            if (negativeGrossProfitOrders.length === 0) {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = '<span style="color: #34c759;"><i class="fas fa-check-circle"></i> Không có đơn hàng nào có lợi nhuận âm</span>';
                negativeGrossProfitList.appendChild(alertItem);
            }

            // Cập nhật đơn hàng chờ mua quá 2 ngày
            updateElementIfExists('waitingToBuyCount', `${waitingToBuyOrders.length} đơn`);
            const waitingToBuyList = document.getElementById('waitingToBuyList');
            if (!waitingToBuyList) {
                console.warn('Không tìm thấy phần tử waitingToBuyList');
                return;
            }
            waitingToBuyList.innerHTML = '';

            waitingToBuyOrders.forEach(order => {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = `
                    <strong>${order.code}</strong>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span><i class="fas fa-user-tie"></i> ${order.staffName}</span>
                        <span><i class="far fa-calendar-alt"></i> Cọc: ${formatDate(order.depositDate)}</span>
                    </div>
                    <div style="color: #ff9500; font-weight: 600; margin-top: 4px;">
                        <i class="fas fa-clock"></i> Đã chờ: ${order.daysSinceDeposit} ngày
                    </div>
                `;
                waitingToBuyList.appendChild(alertItem);
            });

            if (waitingToBuyOrders.length === 0) {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = '<span style="color: #34c759;"><i class="fas fa-check-circle"></i> Không có đơn hàng nào chờ mua quá 2 ngày</span>';
                waitingToBuyList.appendChild(alertItem);
            }

            // Cập nhật đơn hàng có tracking chưa cập nhật
            updateElementIfExists('trackingNotUpdatedCount', `${trackingNotUpdatedOrders.length} đơn`);
            const trackingNotUpdatedList = document.getElementById('trackingNotUpdatedList');
            if (!trackingNotUpdatedList) {
                console.warn('Không tìm thấy phần tử trackingNotUpdatedList');
                return;
            }
            trackingNotUpdatedList.innerHTML = '';

            trackingNotUpdatedOrders.forEach(order => {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = `
                    <strong>${order.code}</strong>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span><i class="fas fa-user-tie"></i> ${order.staffName}</span>
                        <span><i class="far fa-calendar-alt"></i> Cập nhật: ${formatDate(order.lastUpdateDate)}</span>
                    </div>
                    <div style="color: #5856d6; font-weight: 600; margin-top: 4px;">
                        <i class="fas fa-truck"></i> Chưa cập nhật: ${order.daysSinceLastUpdate} ngày
                    </div>
                `;
                trackingNotUpdatedList.appendChild(alertItem);
            });

            if (trackingNotUpdatedOrders.length === 0) {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = '<span style="color: #34c759;"><i class="fas fa-check-circle"></i> Không có đơn hàng nào có tracking chưa cập nhật</span>';
                trackingNotUpdatedList.appendChild(alertItem);
            }

            // Cập nhật đơn hàng quá hạn thanh toán
            updateElementIfExists('overduePaymentCount', `${overduePaymentOrders.length} đơn`);
            const overduePaymentList = document.getElementById('overduePaymentList');
            if (!overduePaymentList) {
                console.warn('Không tìm thấy phần tử overduePaymentList');
                return;
            }
            overduePaymentList.innerHTML = '';

            overduePaymentOrders.forEach(order => {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = `
                    <strong>${order.code}</strong>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span><i class="fas fa-user-tie"></i> ${order.staffName}</span>
                        <span><i class="far fa-calendar-alt"></i> Về kho: ${formatDate(order.vnWarehouseDate)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span style="color: #ff2d55; font-weight: 600;"><i class="fas fa-exclamation-circle"></i> Quá hạn: ${order.daysSinceVNWarehouse} ngày</span>
                        <span style="font-weight: 600;"><i class="fas fa-money-bill-wave"></i> Còn nợ: ${formatter.format(order.remainingAmount)}</span>
                    </div>
                `;
                overduePaymentList.appendChild(alertItem);
            });

            if (overduePaymentOrders.length === 0) {
                const alertItem = document.createElement('div');
                alertItem.className = 'alert-item';
                alertItem.innerHTML = '<span style="color: #34c759;"><i class="fas fa-check-circle"></i> Không có đơn hàng nào quá hạn thanh toán</span>';
                overduePaymentList.appendChild(alertItem);
            }
        }

        // Hàm định dạng ngày tháng
        function formatDate(dateInput) {
            if (!dateInput) return 'N/A';

            // Convert string to Date object if needed
            const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

            // Check if date is valid
            if (isNaN(date.getTime())) return 'N/A';

            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();

            return `${day}/${month}/${year}`;
        }



        // Hiển thị danh sách đơn mua hộ trong bảng admin chi tiết
        function displayAdminPurchaseOrders(orders) {
            const tbody = document.getElementById('adminOrdersTableBody');
            tbody.innerHTML = '';

            if (orders.length === 0) {
                tbody.innerHTML = '<tr><td colspan="16" style="text-align: center;">Không có đơn mua hộ nào</td></tr>';
                return;
            }

            orders.forEach(order => {
                const row = document.createElement('tr');

                // Thêm thuộc tính data cho việc lọc và xóa
                row.setAttribute('data-order-id', order.id);
                row.setAttribute('data-status', order.status || 'pending');
                row.setAttribute('data-payment-status', order.payment_status || 'unpaid');
                row.setAttribute('data-date', order.created_at);

                // Tạo danh sách sản phẩm có thể xem thêm/thu gọn
                const productListHtml = createCollapsibleProductList(order.items);

                // Tạo chuỗi hiển thị số lượng đã mua
                const purchasedInfo = getPurchasedInfo(order.items);

                // Tạo chuỗi hiển thị tracking
                const trackingInfo = getTrackingInfo(order.items, order);

                // Tạo badge trạng thái đơn hàng
                const statusBadge = createStatusBadge(order.status, order);

                // Tính toán thông tin tài chính trước
                let totalPurchasePrice = 0;
                let totalPurchaseAmount = 0;
                let totalSellAmount = 0;
                let totalGrossProfit = 0;
                let avgPurchaseExchangeRate = 0;
                let purchaseExchangeRateCount = 0;

                // Tính tổng giá mua và tỷ giá mua trung bình
                order.items.forEach(item => {
                    const purchasedQty = item.purchased_quantity ? parseFloat(item.purchased_quantity) : 0;
                    const purchasePrice = item.purchase_price ? parseFloat(item.purchase_price) : 0;
                    const purchaseExchangeRate = item.purchase_exchange_rate ? parseFloat(item.purchase_exchange_rate) : 0;
                    const sellPrice = item.price ? parseFloat(item.price) : 0;
                    const sellQty = item.quantity ? parseFloat(item.quantity) : 0;

                    if (purchasePrice > 0 && purchasedQty > 0) {
                        totalPurchasePrice += purchasePrice * purchasedQty;

                        if (purchaseExchangeRate > 0) {
                            totalPurchaseAmount += purchasePrice * purchasedQty * purchaseExchangeRate;
                            avgPurchaseExchangeRate += purchaseExchangeRate;
                            purchaseExchangeRateCount++;
                        }
                    }

                    // Sửa logic: Chỉ tính doanh thu cho số lượng đã mua thực tế
                    if (sellPrice > 0 && sellQty > 0 && purchasedQty > 0) {
                        const actualQuantityForCalculation = Math.min(purchasedQty, sellQty);
                        totalSellAmount += sellPrice * actualQuantityForCalculation * parseFloat(order.exchange_rate);
                    }
                });

                // Tính tỷ giá mua trung bình
                avgPurchaseExchangeRate = purchaseExchangeRateCount > 0 ? avgPurchaseExchangeRate / purchaseExchangeRateCount : 0;

                // Tính lãi Gross (đã được sửa để tính đúng)
                totalGrossProfit = totalSellAmount - totalPurchaseAmount;

                // Tính tỷ lệ đã thanh toán
                const totalOrderAmount = parseFloat(order.total_amount) * parseFloat(order.exchange_rate);
                const paidAmount = parseFloat(order.paid_amount) || 0;
                const paidPercentage = totalOrderAmount > 0 ? ((paidAmount / totalOrderAmount) * 100).toFixed(2) : 0;
                const remainingAmount = totalOrderAmount - paidAmount;

                // Tạo badge trạng thái thanh toán với logic cải tiến
                console.log(`Đơn hàng ${order.id} - Trạng thái thanh toán:`, order.payment_status, 'Ghi chú:', order.note);
                const paymentStatusBadge = createPaymentStatusBadge(order.payment_status, order.note, remainingAmount);

                // Tạo HTML cho dòng
                row.innerHTML = `
                    <td>${formatDate(order.created_at)}</td>
                    <td>${order.order_code || order.id}</td>
                    <td>${order.user_username || (order.user ? order.user.username : 'N/A')}</td>
                    <td>${order.staff_name || 'Chưa phân công'}</td>
                    <td>${productListHtml}</td>
                    <td>${purchasedInfo}</td>
                    <td>${order.total_purchase_price || (totalPurchasePrice > 0 ? totalPurchasePrice.toFixed(2) : 'N/A')}</td>
                    <td>${order.avg_purchase_exchange_rate || (avgPurchaseExchangeRate > 0 ? avgPurchaseExchangeRate.toFixed(2) : 'N/A')}</td>
                    <td>${parseFloat(order.total_amount) > 0 ? parseFloat(order.total_amount).toFixed(2) : 'N/A'}</td>
                    <td>${parseFloat(order.exchange_rate) > 0 ? parseFloat(order.exchange_rate).toFixed(2) : 'N/A'}</td>
                    <td>${parseFloat(order.paid_amount) > 0 ?
                        `${formatter.format(parseFloat(order.paid_amount))}/${formatter.format(totalOrderAmount)} (${paidPercentage}%)`
                        : 'Chưa thanh toán'}</td>
                    <td style="color: ${totalGrossProfit >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">${order.gross_profit ? formatter.format(parseFloat(order.gross_profit)) : (totalGrossProfit !== 0 ? formatter.format(totalGrossProfit) : 'N/A')}</td>
                    <td>${trackingInfo}</td>
                    <td>${statusBadge}</td>
                    <td>${paymentStatusBadge}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-button view-button" data-id="${order.id}" title="Xem chi tiết"><i class="fas fa-eye"></i></button>
                            ${isAdmin ? `<button class="action-button edit-button" data-id="${order.id}" title="Chỉnh sửa"><i class="fas fa-edit"></i></button>` : ''}
                            ${isAdmin ? `<button class="action-button delete-button" data-id="${order.id}" title="Xóa đơn hàng"><i class="fas fa-trash"></i></button>` : ''}
                        </div>
                    </td>
                `;

                tbody.appendChild(row);

                // Thêm sự kiện cho nút xem thêm/thu gọn
                const toggleButton = row.querySelector('.toggle-products');
                if (toggleButton) {
                    toggleButton.addEventListener('click', function() {
                        const productList = this.previousElementSibling;
                        if (productList.classList.contains('product-list-collapsed')) {
                            productList.classList.remove('product-list-collapsed');
                            this.textContent = 'Thu gọn';
                        } else {
                            productList.classList.add('product-list-collapsed');
                            this.textContent = 'Xem thêm';
                        }
                    });
                }

                // Thêm sự kiện cho nút xem chi tiết
                const viewButton = row.querySelector('.view-button');
                if (viewButton) {
                    viewButton.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        viewOrderDetail(orderId);
                    });
                }

                // Thêm sự kiện cho nút chỉnh sửa (chỉ cho admin)
                if (isAdmin) {
                    const editButton = row.querySelector('.edit-button');
                    if (editButton) {
                        editButton.addEventListener('click', function() {
                            const orderId = this.getAttribute('data-id');
                            editOrder(orderId);
                        });
                    }
                }

                // Thêm sự kiện cho nút xóa (chỉ cho admin)
                if (isAdmin) {
                    const deleteButton = row.querySelector('.delete-button');
                    if (deleteButton) {
                        deleteButton.addEventListener('click', function() {
                            const orderId = this.getAttribute('data-id');
                            if (confirm('Bạn có chắc chắn muốn xóa đơn mua hộ này không? Hành động này không thể hoàn tác.')) {
                                deleteOrder(orderId);
                            }
                        });
                    }
                }
            });

            // Initialize admin table enhancements after rendering
            if (isAdmin) {
                // Use setTimeout to ensure DOM is fully rendered
                setTimeout(() => {
                    initializeAdminTableEnhancements();
                }, 100);
            }
        }

        // Initialize admin table enhancements
        function initializeAdminTableEnhancements() {
            console.log('🔧 Initializing admin table enhancements...');

            // Check if already initialized to prevent duplicate event listeners
            if (window.adminTableInitialized) {
                console.log('⚠️ Admin table already initialized, skipping...');
                return;
            }

            try {
                // Verify required elements exist
                const section = document.getElementById('adminDetailSection');
                const table = document.getElementById('adminOrdersTable');

                if (!section || !table) {
                    console.error('❌ Required elements not found:', { section: !!section, table: !!table });
                    return;
                }

                console.log('✅ Required elements found, proceeding with initialization...');

                // Initialize column visibility menu
                initializeColumnVisibility();

                // Initialize show all columns button
                initializeShowAllColumnsButton();

                // Initialize Excel export button
                initializeExcelExportButton();

                // Initialize column resizing
                initializeColumnResizing();

                // Initialize fullscreen mode
                initializeFullscreenMode();

                // Load saved preferences
                loadTablePreferences();

                // Mark as initialized
                window.adminTableInitialized = true;
                console.log('🎉 Admin table enhancements initialized successfully!');

            } catch (error) {
                console.error('❌ Error initializing admin table enhancements:', error);
            }
        }

        // Column visibility functionality
        function initializeColumnVisibility() {
            console.log('🔧 Initializing column visibility...');

            const table = document.getElementById('adminOrdersTable');
            if (!table) {
                console.error('❌ Admin table not found for column visibility');
                return;
            }

            const headers = table.querySelectorAll('thead th');
            console.log(`Found ${headers.length} headers for visibility icons`);

            headers.forEach((header, index) => {
                const columnName = header.textContent.trim();
                const isActionColumn = columnName === 'Thao tác';

                // Skip action column
                if (isActionColumn) {
                    console.log(`Skipping action column (${index})`);
                    return;
                }

                // Remove existing visibility icon if any
                const existingIcon = header.querySelector('.column-visibility-icon');
                if (existingIcon) {
                    existingIcon.remove();
                }

                // Create visibility icon
                const icon = document.createElement('i');
                icon.className = 'fas fa-eye column-visibility-icon';
                icon.title = `Ẩn cột "${columnName}"`;
                icon.setAttribute('data-column-index', index);

                // Add click event
                icon.addEventListener('click', (e) => {
                    e.stopPropagation();
                    e.preventDefault();

                    console.log(`🖱️ Visibility icon clicked for column ${index}: ${columnName}`);

                    const isCurrentlyVisible = !icon.classList.contains('hidden');
                    toggleColumn(index, !isCurrentlyVisible);

                    // Update icon state
                    if (isCurrentlyVisible) {
                        icon.className = 'fas fa-eye-slash column-visibility-icon hidden';
                        icon.title = `Hiện cột "${columnName}"`;
                    } else {
                        icon.className = 'fas fa-eye column-visibility-icon';
                        icon.title = `Ẩn cột "${columnName}"`;
                    }

                    // Save preferences with debounce
                    debounce(saveTablePreferences, 300)();
                });

                header.appendChild(icon);
                console.log(`Added visibility icon to column ${index}: ${columnName}`);
            });

            console.log('✅ Column visibility initialized');
        }

        // Show all columns button functionality
        function initializeShowAllColumnsButton() {
            console.log('🔧 Initializing show all columns button...');

            const btn = document.getElementById('showAllColumnsBtn');
            if (!btn) {
                console.error('❌ Show all columns button not found');
                return;
            }

            // Remove existing event listeners to prevent duplicates
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);

            newBtn.addEventListener('click', (e) => {
                console.log('🖱️ Show all columns button clicked');
                e.preventDefault();
                e.stopPropagation();

                showAllColumns();
            });

            console.log('✅ Show all columns button initialized');
        }

        // Excel export button functionality
        function initializeExcelExportButton() {
            console.log('🔧 Initializing Excel export button...');

            const btn = document.getElementById('exportExcelBtn');
            if (!btn) {
                console.error('❌ Excel export button not found');
                return;
            }

            // Remove existing event listeners to prevent duplicates
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);

            newBtn.addEventListener('click', (e) => {
                console.log('🖱️ Excel export button clicked');
                e.preventDefault();
                e.stopPropagation();

                exportToExcel();
            });

            console.log('✅ Excel export button initialized');
        }

        // Export table data to Excel
        function exportToExcel() {
            console.log('📊 Starting Excel export...');

            try {
                const table = document.getElementById('adminOrdersTable');
                if (!table) {
                    console.error('❌ Table not found for Excel export');
                    showNotification('Không tìm thấy bảng dữ liệu', 'error');
                    return;
                }

                // Get all visible rows (including filtered ones)
                const visibleRows = Array.from(table.querySelectorAll('tbody tr')).filter(row => {
                    return row.style.display !== 'none' && !row.querySelector('td[colspan]');
                });

                if (visibleRows.length === 0) {
                    showNotification('Không có dữ liệu để xuất', 'error');
                    return;
                }

                // Get headers (all columns, including hidden ones)
                const headers = Array.from(table.querySelectorAll('thead th')).map(th => {
                    return th.textContent.trim();
                });

                // Prepare data array
                const data = [];
                data.push(headers); // Add headers as first row

                // Add data rows
                visibleRows.forEach(row => {
                    const rowData = [];
                    const cells = row.querySelectorAll('td');

                    cells.forEach((cell, index) => {
                        let cellText = cell.textContent.trim();

                        // Clean up action buttons column
                        if (index === cells.length - 1) {
                            cellText = 'Thao tác';
                        }

                        rowData.push(cellText);
                    });

                    data.push(rowData);
                });

                // Create workbook and worksheet
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(data);

                // Set column widths
                const colWidths = [
                    { wch: 12 }, // Ngày tạo
                    { wch: 15 }, // Mã đơn
                    { wch: 12 }, // Khách hàng
                    { wch: 12 }, // Nhân viên
                    { wch: 30 }, // Sản phẩm
                    { wch: 10 }, // Đã mua
                    { wch: 12 }, // Giá mua
                    { wch: 12 }, // Tỷ giá mua
                    { wch: 12 }, // Giá bán
                    { wch: 12 }, // Tỷ giá bán
                    { wch: 20 }, // Đã thanh toán
                    { wch: 15 }, // Lãi Gross
                    { wch: 20 }, // Tracking
                    { wch: 15 }, // Trạng thái đơn
                    { wch: 18 }, // Trạng thái thanh toán
                    { wch: 12 }  // Thao tác
                ];
                ws['!cols'] = colWidths;

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'Đơn mua hộ');

                // Generate filename with current date
                const today = new Date();
                const dateStr = today.getDate().toString().padStart(2, '0') +
                               today.getMonth().toString().padStart(2, '0') +
                               today.getFullYear();
                const filename = `don-mua-ho-export-${dateStr}.xlsx`;

                // Save file
                XLSX.writeFile(wb, filename);

                console.log(`✅ Excel file exported: ${filename}`);
                showNotification(`Đã xuất ${visibleRows.length} đơn hàng ra file ${filename}`, 'success');

            } catch (error) {
                console.error('❌ Error exporting to Excel:', error);
                showNotification('Có lỗi xảy ra khi xuất Excel', 'error');
            }
        }

        // Show all hidden columns
        function showAllColumns() {
            console.log('🔄 Showing all hidden columns...');

            const visibilityIcons = document.querySelectorAll('.column-visibility-icon.hidden');
            let restoredCount = 0;

            visibilityIcons.forEach((icon) => {
                const columnIndex = parseInt(icon.getAttribute('data-column-index'));
                const columnName = icon.closest('th').textContent.trim();

                console.log(`Restoring column ${columnIndex}: ${columnName}`);

                // Update icon state
                icon.className = 'fas fa-eye column-visibility-icon';
                icon.title = `Ẩn cột "${columnName}"`;

                // Show the column
                toggleColumn(columnIndex, true);

                restoredCount++;
            });

            if (restoredCount > 0) {
                console.log(`✅ Restored ${restoredCount} hidden columns`);

                // Save preferences
                saveTablePreferences();

                // Show notification
                showNotification(`Đã hiển thị ${restoredCount} cột đã ẩn`, 'success');
            } else {
                console.log('ℹ️ No hidden columns to restore');
                showNotification('Tất cả cột đã được hiển thị', 'info');
            }
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element if it doesn't exist
            let notification = document.getElementById('admin-table-notification');
            if (!notification) {
                notification = document.createElement('div');
                notification.id = 'admin-table-notification';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 10000;
                    opacity: 0;
                    transition: all 0.3s ease;
                    pointer-events: none;
                `;
                document.body.appendChild(notification);
            }

            // Set notification style based on type
            const styles = {
                success: 'background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;',
                error: 'background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;',
                info: 'background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;'
            };

            notification.style.cssText += styles[type] || styles.info;
            notification.textContent = message;

            // Show notification
            notification.style.opacity = '1';
            notification.style.pointerEvents = 'auto';

            // Hide after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.pointerEvents = 'none';
            }, 3000);
        }

        // Toggle column visibility
        function toggleColumn(columnIndex, visible) {
            console.log(`🔄 Toggling column ${columnIndex} visibility to: ${visible}`);

            const table = document.getElementById('adminOrdersTable');
            if (!table) {
                console.error('❌ Table not found for column toggle');
                return;
            }

            const headers = table.querySelectorAll('thead th');
            const rows = table.querySelectorAll('tbody tr');

            // Toggle header
            if (headers[columnIndex]) {
                headers[columnIndex].style.display = visible ? '' : 'none';
                console.log(`Header ${columnIndex} display set to: ${visible ? 'visible' : 'none'}`);
            }

            // Toggle cells in all rows (including dynamically added rows)
            rows.forEach((row, rowIndex) => {
                const cell = row.children[columnIndex];
                if (cell) {
                    cell.style.display = visible ? '' : 'none';
                }
            });

            // Also apply to future rows by adding CSS rule
            const styleId = `column-${columnIndex}-visibility`;
            let existingStyle = document.getElementById(styleId);

            if (!visible) {
                // Create or update style rule to hide column
                if (!existingStyle) {
                    existingStyle = document.createElement('style');
                    existingStyle.id = styleId;
                    document.head.appendChild(existingStyle);
                }
                existingStyle.textContent = `
                    #adminOrdersTable th:nth-child(${columnIndex + 1}),
                    #adminOrdersTable td:nth-child(${columnIndex + 1}) {
                        display: none !important;
                    }
                `;
            } else {
                // Remove style rule to show column
                if (existingStyle) {
                    existingStyle.remove();
                }
            }

            console.log(`✅ Column ${columnIndex} visibility toggled successfully`);
        }

        // Column resizing functionality
        function initializeColumnResizing() {
            console.log('🔧 Initializing column resizing...');

            const table = document.getElementById('adminOrdersTable');
            if (!table) {
                console.error('❌ Admin table not found for column resizing');
                return;
            }

            const headers = table.querySelectorAll('thead th');
            console.log(`Found ${headers.length} headers for resizing`);

            headers.forEach((header, index) => {
                // Skip the last column (actions)
                if (index === headers.length - 1) {
                    console.log(`Skipping last column (${index}) - actions column`);
                    return;
                }

                // Remove existing resizer if any
                const existingResizer = header.querySelector('.column-resizer');
                if (existingResizer) {
                    existingResizer.remove();
                }

                const resizer = document.createElement('div');
                resizer.className = 'column-resizer';
                header.appendChild(resizer);

                console.log(`Added resizer to column ${index}: ${header.textContent.trim()}`);

                let isResizing = false;
                let startX = 0;
                let startWidth = 0;

                resizer.addEventListener('mousedown', (e) => {
                    console.log(`🖱️ Starting resize for column ${index}`);
                    isResizing = true;
                    startX = e.clientX;
                    startWidth = header.offsetWidth;
                    resizer.classList.add('resizing');

                    document.addEventListener('mousemove', handleResize);
                    document.addEventListener('mouseup', stopResize);
                    e.preventDefault();
                    e.stopPropagation();
                });

                function handleResize(e) {
                    if (!isResizing) return;

                    const diff = e.clientX - startX;
                    const newWidth = Math.max(50, startWidth + diff);

                    // Apply width to header
                    header.style.width = newWidth + 'px';
                    header.style.minWidth = newWidth + 'px';
                    header.style.maxWidth = newWidth + 'px';

                    // Apply to all cells in this column using CSS
                    const styleId = `column-${index}-width`;
                    let existingStyle = document.getElementById(styleId);

                    if (!existingStyle) {
                        existingStyle = document.createElement('style');
                        existingStyle.id = styleId;
                        document.head.appendChild(existingStyle);
                    }

                    existingStyle.textContent = `
                        #adminOrdersTable th:nth-child(${index + 1}),
                        #adminOrdersTable td:nth-child(${index + 1}) {
                            width: ${newWidth}px !important;
                            min-width: ${newWidth}px !important;
                            max-width: ${newWidth}px !important;
                        }
                    `;
                }

                function stopResize() {
                    if (isResizing) {
                        console.log(`✅ Finished resizing column ${index}`);
                        isResizing = false;
                        resizer.classList.remove('resizing');

                        // Save preferences with debounce
                        debounce(saveTablePreferences, 400)();

                        document.removeEventListener('mousemove', handleResize);
                        document.removeEventListener('mouseup', stopResize);
                    }
                }
            });
        }

        // Fullscreen mode functionality
        function initializeFullscreenMode() {
            console.log('🔧 Initializing fullscreen mode...');

            const btn = document.getElementById('fullscreenBtn');
            const section = document.getElementById('adminDetailSection');

            console.log('Fullscreen elements:', {
                btn: !!btn,
                section: !!section
            });

            if (!btn || !section) {
                console.error('❌ Fullscreen elements not found');
                return;
            }

            // Remove existing event listeners to prevent duplicates
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);

            newBtn.addEventListener('click', (e) => {
                console.log('🖱️ Fullscreen button clicked');
                e.preventDefault();
                e.stopPropagation();
                toggleFullscreen();
            });

            // ESC key to exit fullscreen
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && section.classList.contains('admin-table-fullscreen')) {
                    console.log('⌨️ ESC key pressed, exiting fullscreen');
                    toggleFullscreen();
                }
            });

            console.log('✅ Fullscreen mode initialized');
        }

        // Toggle fullscreen mode
        function toggleFullscreen() {
            console.log('🔄 Toggling fullscreen mode...');

            const section = document.getElementById('adminDetailSection');
            const btn = document.getElementById('fullscreenBtn');

            if (!section || !btn) {
                console.error('❌ Required elements not found for fullscreen toggle');
                return;
            }

            const icon = btn.querySelector('i');
            if (!icon) {
                console.error('❌ Fullscreen button icon not found');
                return;
            }

            const isCurrentlyFullscreen = section.classList.contains('admin-table-fullscreen');
            console.log('Current fullscreen state:', isCurrentlyFullscreen);

            if (isCurrentlyFullscreen) {
                // Exit fullscreen
                section.classList.remove('admin-table-fullscreen');
                icon.className = 'fas fa-expand';
                btn.title = 'Chế độ toàn màn hình';
                console.log('✅ Exited fullscreen mode');
            } else {
                // Enter fullscreen
                section.classList.add('admin-table-fullscreen');
                icon.className = 'fas fa-compress';
                btn.title = 'Thoát toàn màn hình';
                console.log('✅ Entered fullscreen mode');
            }

            saveTablePreferences();
        }

        // Save table preferences to localStorage
        function saveTablePreferences() {
            const table = document.getElementById('adminOrdersTable');
            const section = document.getElementById('adminDetailSection');

            if (!table || !section) return;

            const preferences = {
                columnWidths: {},
                hiddenColumns: [],
                fullscreen: section.classList.contains('admin-table-fullscreen')
            };

            // Save column widths
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                if (header.style.width) {
                    preferences.columnWidths[index] = header.style.width;
                }
            });

            // Save hidden columns by checking visibility icons
            const visibilityIcons = document.querySelectorAll('.column-visibility-icon');
            visibilityIcons.forEach((icon) => {
                const columnIndex = parseInt(icon.getAttribute('data-column-index'));
                if (icon.classList.contains('hidden')) {
                    preferences.hiddenColumns.push(columnIndex);
                }
            });

            localStorage.setItem('adminTable_preferences', JSON.stringify(preferences));
        }

        // Load table preferences from localStorage
        function loadTablePreferences() {
            const saved = localStorage.getItem('adminTable_preferences');
            if (!saved) return;

            try {
                const preferences = JSON.parse(saved);

                // Apply column widths
                if (preferences.columnWidths) {
                    Object.entries(preferences.columnWidths).forEach(([index, width]) => {
                        const columnIndex = parseInt(index);
                        const styleId = `column-${columnIndex}-width`;
                        let existingStyle = document.getElementById(styleId);

                        if (!existingStyle) {
                            existingStyle = document.createElement('style');
                            existingStyle.id = styleId;
                            document.head.appendChild(existingStyle);
                        }

                        existingStyle.textContent = `
                            #adminOrdersTable th:nth-child(${columnIndex + 1}),
                            #adminOrdersTable td:nth-child(${columnIndex + 1}) {
                                width: ${width} !important;
                                min-width: ${width} !important;
                                max-width: ${width} !important;
                            }
                        `;
                    });
                }

                // Apply hidden columns
                if (preferences.hiddenColumns) {
                    preferences.hiddenColumns.forEach(columnIndex => {
                        const icon = document.querySelector(`.column-visibility-icon[data-column-index="${columnIndex}"]`);
                        if (icon) {
                            const columnName = icon.closest('th').textContent.trim();

                            // Update icon state
                            icon.className = 'fas fa-eye-slash column-visibility-icon hidden';
                            icon.title = `Hiện cột "${columnName}"`;

                            // Hide the column
                            toggleColumn(columnIndex, false);
                        }
                    });
                }

                // Apply fullscreen mode
                if (preferences.fullscreen) {
                    const section = document.getElementById('adminDetailSection');
                    const btn = document.getElementById('fullscreenBtn');
                    const icon = btn.querySelector('i');

                    section.classList.add('admin-table-fullscreen');
                    icon.className = 'fas fa-compress';
                    btn.title = 'Thoát toàn màn hình';
                }

            } catch (error) {
                console.error('Error loading table preferences:', error);
            }
        }

        // Debounce function for performance optimization
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // ===== ORDERS TABLE RESIZE FUNCTIONALITY =====

        // Initialize orders table column resizing
        function initializeOrdersTableResizing() {
            console.log('🔧 Initializing orders table column resizing...');

            const table = document.getElementById('ordersTable');
            if (!table) {
                console.error('❌ Orders table not found for column resizing');
                return;
            }

            const headers = table.querySelectorAll('thead th');
            console.log(`Found ${headers.length} headers for orders table resizing`);

            headers.forEach((header, index) => {
                // Skip the last column (actions)
                if (index === headers.length - 1) {
                    console.log(`Skipping last column (${index}) - actions column`);
                    return;
                }

                // Remove existing resizer if any
                const existingResizer = header.querySelector('.column-resizer');
                if (existingResizer) {
                    existingResizer.remove();
                }

                const resizer = document.createElement('div');
                resizer.className = 'column-resizer';
                header.appendChild(resizer);

                console.log(`Added resizer to orders table column ${index}: ${header.textContent.trim()}`);

                let isResizing = false;
                let startX = 0;
                let startWidth = 0;

                resizer.addEventListener('mousedown', (e) => {
                    console.log(`🖱️ Starting resize for orders table column ${index}`);
                    isResizing = true;
                    startX = e.clientX;
                    startWidth = header.offsetWidth;
                    resizer.classList.add('resizing');

                    document.addEventListener('mousemove', handleResize);
                    document.addEventListener('mouseup', stopResize);
                    e.preventDefault();
                    e.stopPropagation();
                });

                function handleResize(e) {
                    if (!isResizing) return;

                    const diff = e.clientX - startX;
                    const newWidth = Math.max(50, startWidth + diff); // Minimum 50px

                    // Apply width using CSS
                    const styleId = `orders-column-${index}-width`;
                    let existingStyle = document.getElementById(styleId);

                    if (!existingStyle) {
                        existingStyle = document.createElement('style');
                        existingStyle.id = styleId;
                        document.head.appendChild(existingStyle);
                    }

                    existingStyle.textContent = `
                        #ordersTable th:nth-child(${index + 1}),
                        #ordersTable td:nth-child(${index + 1}) {
                            width: ${newWidth}px !important;
                            min-width: ${newWidth}px !important;
                            max-width: ${newWidth}px !important;
                        }
                    `;
                }

                function stopResize() {
                    if (isResizing) {
                        console.log(`✅ Finished resizing orders table column ${index}`);
                        isResizing = false;
                        resizer.classList.remove('resizing');

                        // Save preferences with debounce
                        debounce(saveOrdersTablePreferences, 400)();

                        document.removeEventListener('mousemove', handleResize);
                        document.removeEventListener('mouseup', stopResize);
                    }
                }
            });
        }

        // Save orders table preferences to localStorage
        function saveOrdersTablePreferences() {
            const table = document.getElementById('ordersTable');
            if (!table) return;

            const preferences = {
                columnWidths: {}
            };

            // Save column widths
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                // Check if there's a dynamic style for this column
                const styleElement = document.getElementById(`orders-column-${index}-width`);
                if (styleElement) {
                    // Extract width from the style content
                    const styleContent = styleElement.textContent;
                    const widthMatch = styleContent.match(/width:\s*(\d+)px/);
                    if (widthMatch) {
                        preferences.columnWidths[index] = `${widthMatch[1]}px`;
                    }
                }
            });

            localStorage.setItem('ordersTable_preferences', JSON.stringify(preferences));
            console.log('💾 Orders table preferences saved:', preferences);
        }

        // Load orders table preferences from localStorage
        function loadOrdersTablePreferences() {
            const saved = localStorage.getItem('ordersTable_preferences');
            if (!saved) return;

            try {
                const preferences = JSON.parse(saved);
                console.log('📂 Loading orders table preferences:', preferences);

                // Apply column widths
                if (preferences.columnWidths) {
                    Object.entries(preferences.columnWidths).forEach(([index, width]) => {
                        const columnIndex = parseInt(index);
                        const styleId = `orders-column-${columnIndex}-width`;
                        let existingStyle = document.getElementById(styleId);

                        if (!existingStyle) {
                            existingStyle = document.createElement('style');
                            existingStyle.id = styleId;
                            document.head.appendChild(existingStyle);
                        }

                        existingStyle.textContent = `
                            #ordersTable th:nth-child(${columnIndex + 1}),
                            #ordersTable td:nth-child(${columnIndex + 1}) {
                                width: ${width} !important;
                                min-width: ${width} !important;
                                max-width: ${width} !important;
                            }
                        `;
                    });
                }

            } catch (error) {
                console.error('Error loading orders table preferences:', error);
            }
        }

        // Initialize orders table enhancements
        function initializeOrdersTableEnhancements() {
            console.log('🔧 Initializing orders table enhancements...');

            // Check if already initialized to prevent duplicate event listeners
            if (window.ordersTableInitialized) {
                console.log('⚠️ Orders table already initialized, skipping...');
                return;
            }

            try {
                const table = document.getElementById('ordersTable');
                if (!table) {
                    console.log('⚠️ Orders table not found, will retry later...');
                    return;
                }

                console.log('✅ Orders table found, proceeding with initialization...');

                // Initialize column resizing
                initializeOrdersTableResizing();

                // Load saved preferences
                loadOrdersTablePreferences();

                // Mark as initialized
                window.ordersTableInitialized = true;
                console.log('🎉 Orders table enhancements initialized successfully!');

            } catch (error) {
                console.error('❌ Error initializing orders table enhancements:', error);
            }
        }

        // Show loading indicator for table operations
        function showTableLoading(show = true) {
            const table = document.getElementById('adminOrdersTable');
            if (!table) return;

            if (show) {
                table.classList.add('table-loading');
            } else {
                table.classList.remove('table-loading');
            }
        }

        // Force re-initialize admin table enhancements (for debugging)
        function forceReinitializeAdminTable() {
            console.log('🔄 Force re-initializing admin table...');

            // Reset initialization flag
            window.adminTableInitialized = false;

            // Clear any existing event listeners by removing and re-adding elements
            const section = document.getElementById('adminDetailSection');
            if (section) {
                // Re-initialize after a short delay
                setTimeout(() => {
                    initializeAdminTableEnhancements();
                }, 100);
            } else {
                console.error('❌ Admin detail section not found');
            }
        }

        // Make force reinitialize available globally
        window.forceReinitializeAdminTable = forceReinitializeAdminTable;

        // Make show all columns available globally for testing
        window.showAllColumns = showAllColumns;

        // Make orders table functions available globally for testing
        window.initializeOrdersTableEnhancements = initializeOrdersTableEnhancements;
        window.saveOrdersTablePreferences = saveOrdersTablePreferences;
        window.loadOrdersTablePreferences = loadOrdersTablePreferences;

        // Debug function for orders table features
        function debugOrdersTableFeatures() {
            const table = document.getElementById('ordersTable');
            const resizers = table ? table.querySelectorAll('.column-resizer') : [];
            const preferences = localStorage.getItem('ordersTable_preferences');

            console.log('=== ORDERS TABLE DEBUG INFO ===');
            console.log('Table found:', !!table);
            console.log('Resizers count:', resizers.length);
            console.log('Initialized:', !!window.ordersTableInitialized);
            console.log('Preferences:', preferences ? JSON.parse(preferences) : 'None');
            console.log('Headers count:', table ? table.querySelectorAll('thead th').length : 0);

            return {
                table: !!table,
                resizers: resizers.length,
                initialized: !!window.ordersTableInitialized,
                preferences: preferences ? JSON.parse(preferences) : null,
                headers: table ? table.querySelectorAll('thead th').length : 0
            };
        }

        // Make debug function available globally
        window.debugOrdersTableFeatures = debugOrdersTableFeatures;

        // Áp dụng bộ lọc cho danh sách đơn mua hộ
        function applyOrderFilter() {
            const searchInput = document.getElementById('orderSearchInput');
            const statusFilter = document.getElementById('orderStatusFilter');
            const paymentStatusFilter = document.getElementById('paymentStatusFilter');
            const startDateFilter = document.getElementById('orderStartDate');
            const endDateFilter = document.getElementById('orderEndDate');
            const manageOrderSearchInput = document.getElementById('manageOrderSearchInput');

            // Lấy giá trị từ các trường lọc
            const searchValue = searchInput ? searchInput.value.toLowerCase() : '';
            const statusValue = statusFilter ? statusFilter.value : '';
            const paymentStatusValue = paymentStatusFilter ? paymentStatusFilter.value : '';
            const startDate = startDateFilter && startDateFilter.value ? new Date(startDateFilter.value) : null;
            const endDate = endDateFilter && endDateFilter.value ? new Date(endDateFilter.value) : null;
            const manageSearchValue = manageOrderSearchInput ? manageOrderSearchInput.value.toLowerCase() : '';

            // Áp dụng bộ lọc cho bảng đơn mua hộ thông thường (tab Tạo đơn mua hộ)
            applyFilterToTable('#ordersTableBody', 'td[colspan="10"]', searchValue, statusValue, paymentStatusValue, startDate, endDate);

            // Áp dụng bộ lọc cho bảng admin chi tiết (tab Quản lý đơn)
            applyFilterToTable('#adminOrdersTableBody', 'td[colspan="16"]', manageSearchValue, statusValue, paymentStatusValue, startDate, endDate);
        }

        // Hàm áp dụng bộ lọc cho một bảng cụ thể
        function applyFilterToTable(tableSelector, colspanSelector, searchValue, statusValue, paymentStatusValue, startDate, endDate) {
            // Lấy tất cả các dòng trong bảng
            const rows = document.querySelectorAll(`${tableSelector} tr`);
            let visibleCount = 0;

            rows.forEach(row => {
                // Bỏ qua dòng thông báo không có đơn mua hộ
                if (row.querySelector(colspanSelector)) {
                    return;
                }

                // Lấy dữ liệu từ các cột (cập nhật sau khi thêm cột ngày tạo)
                const orderId = row.querySelector('td:nth-child(2)').textContent.toLowerCase(); // Mã đơn giờ là cột thứ 2
                const customer = row.querySelector('td:nth-child(3)').textContent.toLowerCase(); // Khách hàng giờ là cột thứ 3
                const status = row.getAttribute('data-status') || '';
                const paymentStatus = row.getAttribute('data-payment-status') || '';
                const orderDate = row.getAttribute('data-date') ? new Date(row.getAttribute('data-date')) : null;

                // Kiểm tra điều kiện lọc
                const matchSearch = !searchValue || orderId.includes(searchValue) || customer.includes(searchValue);
                const matchStatus = !statusValue || status === statusValue;
                const matchPaymentStatus = !paymentStatusValue || paymentStatus === paymentStatusValue;
                const matchStartDate = !startDate || (orderDate && orderDate >= startDate);
                const matchEndDate = !endDate || (orderDate && orderDate <= endDate);

                // Hiển thị hoặc ẩn dòng dựa trên kết quả lọc
                const visible = matchSearch && matchStatus && matchPaymentStatus && matchStartDate && matchEndDate;
                row.style.display = visible ? '' : 'none';

                if (visible) {
                    visibleCount++;
                }
            });

            // Hiển thị số lượng kết quả nếu là bảng ordersTable
            if (tableSelector === '#ordersTableBody') {
                const resultCountElement = document.getElementById('orderResultCount');
                if (resultCountElement) {
                    resultCountElement.textContent = `Hiển thị ${visibleCount} đơn mua hộ`;
                }
            }

            // Hiển thị thông báo nếu không có kết quả
            if (visibleCount === 0) {
                const tbody = document.querySelector(tableSelector);
                if (tbody && tbody.querySelectorAll('tr:not([style*="display: none"])').length === 0) {
                    const colspan = tableSelector === '#adminOrdersTableBody' ? '16' : '10';
                    tbody.innerHTML = `<tr><td colspan="${colspan}" style="text-align: center;">Không có đơn mua hộ nào phù hợp với bộ lọc</td></tr>`;
                }
            }
        }

        // Hiển thị danh sách đơn mua hộ
        function displayPurchaseOrders(orders) {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';

            if (orders.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">Không có đơn mua hộ nào</td></tr>';
                return;
            }

            orders.forEach(order => {
                const row = document.createElement('tr');

                // Thêm thuộc tính data cho việc lọc và xóa
                row.setAttribute('data-order-id', order.id);
                row.setAttribute('data-status', order.status || 'pending');
                row.setAttribute('data-payment-status', order.payment_status || 'unpaid');
                row.setAttribute('data-date', order.created_at);

                // Định dạng ngày tháng
                const date = new Date(order.created_at);

                // Tạo danh sách sản phẩm có thể xem thêm/thu gọn
                const productListHtml = createCollapsibleProductList(order.items);

                // Tạo chuỗi hiển thị số lượng đã mua
                const purchasedInfo = getPurchasedInfo(order.items);

                // Tạo chuỗi hiển thị tracking
                const trackingInfo = getTrackingInfo(order.items, order);

                // Tạo badge trạng thái
                const statusBadge = createStatusBadge(order.status, order);

                // Tạo badge trạng thái thanh toán với logic cải tiến
                const totalOrderAmount = parseFloat(order.total_amount) * parseFloat(order.exchange_rate);
                const paidAmount = parseFloat(order.paid_amount) || 0;
                const remainingAmount = totalOrderAmount - paidAmount;
                const paymentStatusBadge = createPaymentStatusBadge(order.payment_status, order.note, remainingAmount);

                // Tạo HTML cho dòng
                row.innerHTML = `
                    <td>${formatDate(order.created_at)}</td>
                    <td>${order.order_code || order.id}</td>
                    <td>${order.user_username || (order.user ? order.user.username : 'N/A')}</td>
                    <td>${order.staff_name || 'Chưa phân công'}</td>
                    <td>${productListHtml}</td>
                    <td>${purchasedInfo}</td>
                    <td>${trackingInfo}</td>
                    <td>${statusBadge}</td>
                    <td>${paymentStatusBadge}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-button view-button" data-id="${order.id}" title="Xem chi tiết"><i class="fas fa-eye"></i></button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);

                // Thêm sự kiện cho nút xem thêm/thu gọn
                const toggleButton = row.querySelector('.toggle-products');
                if (toggleButton) {
                    toggleButton.addEventListener('click', function() {
                        const productList = this.previousElementSibling;
                        if (productList.classList.contains('product-list-collapsed')) {
                            productList.classList.remove('product-list-collapsed');
                            this.textContent = 'Thu gọn';
                        } else {
                            productList.classList.add('product-list-collapsed');
                            this.textContent = 'Xem thêm';
                        }
                    });
                }

                // Thêm sự kiện cho nút xem chi tiết
                const viewButton = row.querySelector('.view-button');
                if (viewButton) {
                    viewButton.addEventListener('click', function() {
                        const orderId = this.getAttribute('data-id');
                        viewOrderDetail(orderId);
                    });
                }


            });

            // Áp dụng bộ lọc sau khi hiển thị danh sách
            applyOrderFilter();

            // Initialize orders table enhancements after data is loaded
            setTimeout(() => {
                initializeOrdersTableEnhancements();
            }, 100);
        }

        // Tạo danh sách sản phẩm có thể xem thêm/thu gọn
        function createCollapsibleProductList(items) {
            if (!items || items.length === 0) {
                return '<span class="text-muted">Không có sản phẩm</span>';
            }

            let html = '<ul class="product-list product-list-collapsed">';

            items.forEach(item => {
                // Đảm bảo hiển thị tên sản phẩm ngay cả khi API trả về dữ liệu khác nhau
                const productName = item.product_name || 'Không có tên';
                const productVariant = item.product_variant || '';

                html += `<li>${productName}${productVariant ? ` - ${productVariant}` : ''}</li>`;
            });

            html += '</ul>';

            // Chỉ hiển thị nút xem thêm nếu có nhiều hơn 1 sản phẩm
            if (items.length > 1) {
                html += '<span class="toggle-products">Xem thêm</span>';
            }

            return html;
        }

        // Lấy thông tin số lượng đã mua
        function getPurchasedInfo(items) {
            if (!items || items.length === 0) {
                return 'N/A';
            }

            let totalPurchased = 0;
            let totalRequired = 0;

            items.forEach(item => {
                // Đảm bảo chuyển đổi sang số nếu API trả về chuỗi
                const purchasedQty = item.purchased_quantity ? parseFloat(item.purchased_quantity) : 0;
                const requiredQty = item.quantity ? parseFloat(item.quantity) : 0;

                totalPurchased += purchasedQty;
                totalRequired += requiredQty;
            });

            return `${totalPurchased}/${totalRequired}`;
        }

        // Lấy thông tin tracking
        function getTrackingInfo(items, order) {
            // Kiểm tra nếu order có tracking_numbers từ API
            if (order && order.tracking_numbers && order.tracking_numbers.length > 0) {
                return formatTrackingNumbers(order.tracking_numbers);
            }

            // Kiểm tra nếu order có trackings từ API
            if (order && order.trackings && order.trackings.length > 0) {
                const trackingNumbers = order.trackings.map(tracking => tracking.tracking_number);
                return formatTrackingNumbers(trackingNumbers);
            }

            // Fallback: Kiểm tra từ items nếu không có tracking_numbers trong order
            if (!items || items.length === 0) {
                return 'N/A';
            }

            const trackingNumbers = [];

            // Kiểm tra nếu items có thuộc tính tracking_number
            items.forEach(item => {
                if (item.tracking_number && !trackingNumbers.includes(item.tracking_number)) {
                    trackingNumbers.push(item.tracking_number);
                }
            });

            // Nếu không có tracking_number trong items, trả về "Chưa có"
            if (trackingNumbers.length === 0) {
                return '<span class="text-muted">Chưa có</span>';
            }

            return formatTrackingNumbers(trackingNumbers);
        }

        // Format tracking numbers để hiển thị trên nhiều dòng
        function formatTrackingNumbers(trackingNumbers) {
            if (!trackingNumbers || trackingNumbers.length === 0) {
                return '<span class="text-muted">Chưa có</span>';
            }

            const trackingHtml = trackingNumbers.map(tracking =>
                `<span class="tracking-number">${tracking}</span>`
            ).join('');

            return `<div class="tracking-numbers">${trackingHtml}</div>`;
        }

        // Tạo badge trạng thái
        function createStatusBadge(status, order) {
            let badgeClass = '';
            let statusText = '';

            // Sử dụng order_status từ API nếu có
            if (order && order.order_status) {
                statusText = order.order_status;

                // Xác định class dựa trên trạng thái
                switch (statusText) {
                    case 'Chờ mua':
                        badgeClass = 'status-pending';
                        break;
                    case 'Chờ nhận hàng':
                        badgeClass = 'status-processing';
                        break;
                    case 'Đã nhập kho':
                        badgeClass = 'status-shipped';
                        break;
                    case 'Đang vận chuyển về VN':
                        badgeClass = 'status-shipped';
                        break;
                    case 'Đã nhập kho VN':
                        badgeClass = 'status-shipped';
                        break;
                    case 'Đã hoàn thành':
                        badgeClass = 'status-completed';
                        break;
                    default:
                        badgeClass = 'status-pending';
                }

                return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
            }

            // Fallback: Sử dụng status cũ nếu không có order_status
            switch (status) {
                case 'pending':
                    badgeClass = 'status-pending';
                    statusText = 'Chờ mua';
                    break;
                case 'processing':
                    badgeClass = 'status-processing';
                    statusText = 'Đang xử lý';
                    break;
                case 'shipped':
                    badgeClass = 'status-shipped';
                    statusText = 'Đã gửi hàng';
                    break;
                case 'delivered':
                    badgeClass = 'status-completed';
                    statusText = 'Đã giao hàng';
                    break;
                default:
                    badgeClass = 'status-pending';
                    statusText = 'Chờ mua';
            }

            return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
        }

        // Tạo badge trạng thái thanh toán với logic cải tiến
        function createPaymentStatusBadge(paymentStatus, note, remainingAmount = null) {
            let badgeClass = '';
            let statusText = '';

            // Kiểm tra tolerance: nếu số tiền còn lại ≤ 1 VNĐ, coi như đã thanh toán
            if (remainingAmount !== null && remainingAmount <= 1 && remainingAmount >= 0) {
                badgeClass = 'status-completed';
                statusText = 'Đã thanh toán';
                return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
            }

            // Kiểm tra nếu có ghi chú [ĐÃ CỌC]
            if (note && note.includes('[ĐÃ CỌC]')) {
                badgeClass = 'status-deposited';
                statusText = 'Đã cọc';
                return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
            }

            switch (paymentStatus) {
                case 'completed':
                    badgeClass = 'status-completed';
                    statusText = 'Đã thanh toán';
                    break;
                case 'deposited':
                    badgeClass = 'status-deposited';
                    statusText = 'Đã cọc';
                    break;
                case 'paid':
                    badgeClass = 'status-completed';
                    statusText = 'Đã thanh toán';
                    break;
                case 'pending':
                case 'unpaid':
                default:
                    badgeClass = 'status-pending';
                    statusText = 'Chưa thanh toán';
            }

            return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
        }

        // Hàm tính toán số tiền cần thanh toán với logic cải tiến
        function calculatePaymentAmount(purchaseOrder, totalAmount, calculatedDepositAmount, calculatedRemainingAmount, paidAmount) {
            let paymentAmount;
            
            if (purchaseOrder.payment_status === 'pending') {
                // Nếu chưa thanh toán gì, hiển thị số tiền cọc cần thanh toán
                paymentAmount = calculatedDepositAmount - paidAmount;
                if (paymentAmount <= 0) {
                    paymentAmount = calculatedRemainingAmount > 0 ? calculatedRemainingAmount : totalAmount;
                }
            } else if (purchaseOrder.payment_status === 'deposited') {
                // Nếu đã cọc, hiển thị số tiền còn lại cần thanh toán
                paymentAmount = calculatedRemainingAmount > 0 ? calculatedRemainingAmount : 0;
            } else {
                // Các trường hợp khác, sử dụng remaining_amount
                paymentAmount = purchaseOrder.remaining_amount > 0 ? purchaseOrder.remaining_amount : 0;
            }

            return paymentAmount;
        }

        // Hàm kiểm tra và cập nhật trạng thái thanh toán khi admin sửa đơn
        function checkAndUpdatePaymentStatusAfterEdit(orderId, newTotalAmount, currentPaidAmount, currentPaymentStatus) {
            const remainingAmount = newTotalAmount - currentPaidAmount;
            
            // Nếu đơn hàng hiện tại có trạng thái "Đã thanh toán" nhưng sau khi sửa còn nợ > 0
            if (currentPaymentStatus === 'completed' && remainingAmount > 1) {
                // Cần chuyển trạng thái từ "Đã thanh toán" sang "Đã cọc"
                return {
                    shouldUpdate: true,
                    newStatus: 'deposited',
                    reason: 'Đơn hàng được sửa làm tăng tổng tiền, chuyển từ "Đã thanh toán" sang "Đã cọc"'
                };
            }
            
            // Nếu số tiền còn lại ≤ 1 VNĐ, chuyển sang "Đã thanh toán"
            if (remainingAmount <= 1 && remainingAmount >= 0 && currentPaymentStatus !== 'completed') {
                return {
                    shouldUpdate: true,
                    newStatus: 'completed',
                    reason: 'Số tiền còn lại ≤ 1 VNĐ, tự động chuyển sang "Đã thanh toán"'
                };
            }

            return {
                shouldUpdate: false,
                newStatus: currentPaymentStatus,
                reason: 'Không cần thay đổi trạng thái'
            };
        }

        // Hàm cập nhật trạng thái thanh toán sau khi admin sửa đơn
        async function updatePaymentStatusAfterEdit(orderId, newStatus, reason) {
            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}/update-payment-status`, {
                    method: 'POST',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        payment_status: newStatus,
                        reason: reason
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Đã cập nhật trạng thái thanh toán:', result.message);
                    return true;
                } else {
                    const error = await response.json();
                    console.error('Lỗi cập nhật trạng thái thanh toán:', error.message);
                    return false;
                }
            } catch (error) {
                console.error('Lỗi khi gọi API cập nhật trạng thái thanh toán:', error);
                return false;
            }
        }

        // Xem chi tiết đơn mua hộ
        async function viewOrderDetail(orderId, tab = 'all') {
            currentOrderId = orderId;
            showLoader(true, 'Đang tải chi tiết đơn hàng...');

            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}`, {
                    headers: getAuthHeader()
                });

                if (response.ok) {
                    const data = await response.json();
                    displayOrderDetail(data, tab);
                } else {
                    console.error('Không thể tải chi tiết đơn mua hộ');
                    alert('Không thể tải chi tiết đơn mua hộ');
                }
            } catch (error) {
                console.error('Lỗi tải chi tiết đơn mua hộ:', error);
                alert('Có lỗi xảy ra khi tải chi tiết đơn mua hộ');
            } finally {
                showLoader(false);
            }
        }

        // Hiển thị chi tiết đơn mua hộ
        function displayOrderDetail(data, tab) {
            const { purchaseOrder, items, trackings, payments } = data;

            // Log để debug
            console.log('Hiển thị chi tiết đơn mua hộ:', purchaseOrder);
            console.log('Trạng thái thanh toán:', purchaseOrder.payment_status);
            console.log('Ghi chú:', purchaseOrder.note);

            // Hiển thị modal chi tiết đơn mua hộ
            const modal = document.getElementById('orderDetailModal');
            const modalContent = document.getElementById('orderDetailContent');

            // Định dạng ngày tháng
            const date = new Date(purchaseOrder.created_at);
            const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;

            // Tính toán số tiền cần thanh toán
            const totalAmount = purchaseOrder.total_amount * purchaseOrder.exchange_rate;
            const paidAmount = purchaseOrder.paid_amount || 0;
            const calculatedRemainingAmount = totalAmount - paidAmount;

            // Tính toán số tiền cọc chính xác dựa trên tỷ lệ cọc
            const calculatedDepositAmount = (totalAmount * purchaseOrder.deposit_percentage) / 100;

            // Xác định số tiền cần thanh toán dựa trên trạng thái với logic cải tiến
            let paymentAmount = calculatePaymentAmount(purchaseOrder, totalAmount, calculatedDepositAmount, calculatedRemainingAmount, paidAmount);

            // Áp dụng logic làm tròn thông minh cho số tiền thanh toán QR
            // Quy tắc làm tròn:
            // - Nếu số tiền < 1000: làm tròn đến hàng đơn vị
            // - Nếu số tiền >= 1000 và < 10000: làm tròn đến hàng chục
            // - Nếu số tiền >= 10000: làm tròn đến hàng trăm
            let roundedPaymentAmount;
            
            if (paymentAmount < 1000) {
                // Làm tròn đến hàng đơn vị (VD: 123.45 -> 123)
                roundedPaymentAmount = Math.round(paymentAmount);
            } else if (paymentAmount < 10000) {
                // Làm tròn đến hàng chục (VD: 1234.56 -> 1230)
                roundedPaymentAmount = Math.round(paymentAmount / 10) * 10;
            } else {
                // Làm tròn đến hàng trăm (VD: 12345.67 -> 12300)
                roundedPaymentAmount = Math.round(paymentAmount / 100) * 100;
            }
            
            // Đảm bảo số tiền không âm và không bằng 0
            if (roundedPaymentAmount <= 0) {
                roundedPaymentAmount = Math.max(1, Math.round(paymentAmount));
            }
            
            // Xử lý tolerance: nếu số tiền gốc và số tiền làm tròn chênh lệch <= 1 VNĐ, sử dụng số tiền gốc
            if (Math.abs(paymentAmount - roundedPaymentAmount) <= 1) {
                roundedPaymentAmount = Math.round(paymentAmount);
            }
            
            console.log(`Làm tròn số tiền QR thanh toán: ${paymentAmount} -> ${roundedPaymentAmount}`);
            
            const qrPaymentUrl = `https://qr.sepay.vn/img?bank=TPBank&acc=TRUSTMOVE&template=qronly&amount=${roundedPaymentAmount}&des=${purchaseOrder.order_code}`;

            // Tạo HTML cho thông tin đơn mua hộ
            let html = `
                <h3>Chi tiết đơn mua hộ</h3>
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div>
                        <p><strong>Mã đơn:</strong> ${purchaseOrder.order_code}</p>
                        <p><strong>Khách hàng:</strong> ${purchaseOrder.user_username}</p>
                        <p><strong>Nhân viên phụ trách:</strong> ${purchaseOrder.staff_name || 'Chưa phân công'}</p>
                        <p><strong>Ngày tạo:</strong> ${formattedDate}</p>
                        <p><strong>Tỷ giá:</strong> ${purchaseOrder.exchange_rate}</p>
                        <p><strong>Tỷ lệ cọc:</strong> ${purchaseOrder.deposit_percentage}%</p>
                    </div>
                    <div>
                        <p><strong>Tổng tiền:</strong> ${formatter.format(totalAmount)} (${purchaseOrder.total_amount})</p>
                        <p><strong>Cọc:</strong> ${formatter.format(calculatedDepositAmount)}</p>
                        <p><strong>Đã thanh toán:</strong> ${formatter.format(paidAmount)}</p>
                        <p><strong>Còn lại:</strong> ${formatter.format(calculatedRemainingAmount)}</p>
                        <p><strong>Trạng thái thanh toán:</strong>
                            <span class="status-badge status-${purchaseOrder.payment_status === 'deposited' ? 'deposited' :
                                                              (purchaseOrder.note && purchaseOrder.note.includes('[ĐÃ CỌC]') ? 'deposited' :
                                                               purchaseOrder.payment_status)}">
                                ${purchaseOrder.payment_status === 'deposited' ? 'Đã cọc' :
                                  purchaseOrder.payment_status === 'pending' ?
                                  (purchaseOrder.note && purchaseOrder.note.includes('[ĐÃ CỌC]') ? 'Đã cọc' : 'Chưa thanh toán') :
                                  purchaseOrder.payment_status === 'completed' ? 'Đã thanh toán' : 'Chưa thanh toán'}
                            </span>
                        </p>
                    </div>
                </div>
            `;

            // Hiển thị QR thanh toán nếu chưa thanh toán hoàn thành
            if (purchaseOrder.payment_status !== 'completed') {
                // Log để debug
                console.log('Hiển thị QR thanh toán:', {
                    payment_status: purchaseOrder.payment_status,
                    deposit_percentage: purchaseOrder.deposit_percentage,
                    calculated_deposit_amount: calculatedDepositAmount,
                    total_amount: totalAmount,
                    paid_amount: paidAmount,
                    calculated_remaining: calculatedRemainingAmount,
                    payment_amount: paymentAmount
                });
                html += `
                    <div style="background: linear-gradient(135deg, #8b0000 0%, #c21807 100%); padding: 20px; border-radius: 15px; margin: 20px 0; text-align: center; color: white; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                        <h4 style="margin: 0 0 15px 0; color: white; font-size: 18px;">
                            <i class="fas fa-qrcode" style="margin-right: 8px;"></i>
                            Thanh toán đơn hàng
                        </h4>
                        <div style="background: white; padding: 15px; border-radius: 10px; display: inline-block; margin-bottom: 15px;">
                            <img src="${qrPaymentUrl}" alt="QR Thanh toán" style="width: 180px; height: 180px; display: block;">
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 12px; border-radius: 8px; margin-top: 10px;">
                            <p style="margin: 5px 0; font-size: 14px; font-weight: 500;">
                                <i class="fas fa-money-bill-wave" style="margin-right: 8px;"></i>
                                Số tiền: <span style="color: #FFD700; font-weight: bold; font-size: 16px;">${formatter.format(roundedPaymentAmount)}</span>
                            </p>
                            ${Math.abs(paymentAmount - roundedPaymentAmount) > 1 ?
                                `<p style="margin: 2px 0; font-size: 11px; opacity: 0.8;">
                                    <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                                    Đã làm tròn từ ${formatter.format(paymentAmount)}
                                </p>` : ''
                            }
                            <p style="margin: 5px 0; font-size: 14px;">
                                <i class="fas fa-comment-alt" style="margin-right: 8px;"></i>
                                Nội dung: <span style="font-weight: bold;">${purchaseOrder.order_code}</span>
                            </p>
                            <p style="margin: 5px 0; font-size: 12px; opacity: 0.9;">
                                <i class="fas fa-university" style="margin-right: 8px;"></i>
                                TPBank - TRUSTMOVE
                            </p>
                        </div>

                        <!-- QR tra cứu đơn hàng -->
                        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                            <p style="margin: 0 0 10px 0; font-size: 14px; opacity: 0.9;">
                                <i class="fas fa-search" style="margin-right: 8px;"></i>
                                QR tra cứu đơn hàng
                            </p>
                            <div style="background: white; padding: 8px; border-radius: 8px; display: inline-block;">
                                <img src="/api/qrcode?data=${encodeURIComponent(window.location.origin + '/purchase-order-lookup.html?code=' + purchaseOrder.order_code)}&size=100" alt="QR Tra cứu" style="width: 80px; height: 80px; display: block;">
                            </div>
                        </div>
                    </div>
                `;
            }

            // Hiển thị danh sách sản phẩm nếu tab là 'all' hoặc 'products'
            if (tab === 'all' || tab === 'products') {
                html += `
                    <h4>Danh sách sản phẩm</h4>
                    <table style="width: 100%; margin-bottom: 20px;">
                        <thead>
                            <tr>
                                <th>Tên sản phẩm</th>
                                <th>Phân loại</th>
                                <th>Số lượng</th>
                                <th>Giá bán($)</th>
                                <th>Thành tiền</th>
                                ${isAdmin ? '<th>Thao tác</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
                `;

                items.forEach(item => {
                    html += `
                        <tr>
                            <td>${item.product_name}</td>
                            <td>${item.product_variant || '-'}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price}</td>
                            <td>${formatter.format(item.price * item.quantity * purchaseOrder.exchange_rate)}</td>
                            ${isAdmin ? `
                                <td>
                                    <button class="btn btn-primary btn-sm update-item-btn" data-id="${item.id}" data-exchange-rate="${purchaseOrder.exchange_rate}">
                                        Cập nhật
                                    </button>
                                </td>
                            ` : ''}
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            }

            // Hiển thị thông tin đã mua nếu tab là 'all' hoặc 'purchased'
            if ((tab === 'all' || tab === 'purchased') && isAdmin) {
                html += `
                    <h4>Thông tin đã mua</h4>
                    <table style="width: 100%; margin-bottom: 20px;">
                        <thead>
                            <tr>
                                <th>Tên sản phẩm</th>
                                <th>Đã mua</th>
                                <th>Giá mua($)</th>
                                <th>Tỷ giá mua</th>
                                <th>Thành tiền (VND)</th>
                                <th>Lãi Gross</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                items.forEach(item => {
                    const purchasedQuantity = item.purchased_quantity || 0;
                    const purchasePrice = item.purchase_price || 0;
                    const purchaseExchangeRate = item.purchase_exchange_rate || 0;

                    // Sửa logic: Tính lãi gross dựa trên số lượng đã mua thực tế
                    // Chỉ tính lãi cho phần đã mua, không tính cho phần chưa mua
                    const actualQuantityForCalculation = Math.min(purchasedQuantity, item.quantity);
                    const sellTotal = item.price * actualQuantityForCalculation * purchaseOrder.exchange_rate;
                    const buyTotal = purchasePrice * purchasedQuantity * purchaseExchangeRate;
                    const grossProfit = sellTotal - buyTotal;

                    // Tạo tooltip giải thích tính toán
                    const tooltipText = purchasedQuantity > 0 && purchasePrice > 0 && purchaseExchangeRate > 0
                        ? `Công thức: (${item.price} × ${actualQuantityForCalculation} × ${purchaseOrder.exchange_rate}) - (${purchasePrice} × ${purchasedQuantity} × ${purchaseExchangeRate}) = ${formatter.format(grossProfit)}`
                        : 'Chưa đủ thông tin để tính toán';

                    // Tạo cảnh báo nếu cần
                    let warningIcon = '';
                    if (purchasedQuantity !== item.quantity && purchasedQuantity > 0) {
                        warningIcon = ' ⚠️';
                    }
                    if (purchasePrice > item.price && purchasePrice > 0) {
                        warningIcon += ' 📈';
                    }

                    html += `
                        <tr>
                            <td>${item.product_name}</td>
                            <td>${purchasedQuantity}/${item.quantity}${warningIcon}</td>
                            <td>${purchasePrice > 0 ? purchasePrice : '-'}</td>
                            <td>${purchaseExchangeRate > 0 ? purchaseExchangeRate : '-'}</td>
                            <td>${buyTotal > 0 ? formatter.format(buyTotal) : '-'}</td>
                            <td title="${tooltipText}" style="cursor: help; ${grossProfit < 0 ? 'color: #dc3545; font-weight: 600;' : grossProfit > 0 ? 'color: #28a745; font-weight: 600;' : ''}">${grossProfit !== 0 ? formatter.format(grossProfit) : '-'}</td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                `;
            }

            // Hiển thị danh sách tracking nếu tab là 'all' hoặc 'tracking'
            if (tab === 'all' || tab === 'tracking') {
                html += `
                    <h4>Danh sách tracking</h4>
                `;

                if (trackings.length === 0) {
                    html += `<p>Chưa có tracking nào</p>`;
                } else {
                    html += `
                        <table style="width: 100%; margin-bottom: 20px;">
                            <thead>
                                <tr>
                                    <th>Tracking number</th>
                                    <th>Sản phẩm</th>
                                    <th>Kho</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    trackings.forEach(tracking => {
                        // Tìm sản phẩm tương ứng
                        const item = items.find(item => item.id === tracking.purchase_order_item_id);
                        const itemName = item ? item.product_name : 'Không xác định';

                        // Định dạng ngày tháng
                        const trackingDate = new Date(tracking.created_at);
                        const formattedTrackingDate = `${trackingDate.getDate()}/${trackingDate.getMonth() + 1}/${trackingDate.getFullYear()}`;

                        // Định dạng trạng thái
                        let statusText = '';
                        switch (tracking.status) {
                            case 'chonhanhang':
                                statusText = 'Chờ nhận hàng';
                                break;
                            case 'nhapkho':
                                statusText = 'Đã nhập kho';
                                break;
                            case 'dangvevn':
                                statusText = 'Đang về VN';
                                break;
                            case 'nhapkhovn':
                                statusText = 'Đã nhập kho VN';
                                break;
                            case 'hoanthanh':
                                statusText = 'Hoàn thành';
                                break;
                            default:
                                statusText = tracking.status;
                        }

                        html += `
                            <tr>
                                <td>${tracking.tracking_number}</td>
                                <td>${itemName}</td>
                                <td>${tracking.warehouse_name || '-'}</td>
                                <td>${statusText}</td>
                                <td>${formattedTrackingDate}</td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;
                }
            }

            // Hiển thị lịch sử thanh toán nếu tab là 'all' hoặc 'status'
            if (tab === 'all' || tab === 'status') {
                html += `
                    <h4>Lịch sử thanh toán</h4>
                `;

                if (payments.length === 0) {
                    html += `<p>Chưa có thanh toán nào</p>`;
                } else {
                    html += `
                        <table style="width: 100%; margin-bottom: 20px;">
                            <thead>
                                <tr>
                                    <th>Ngày thanh toán</th>
                                    <th>Số tiền</th>
                                    <th>Mã giao dịch</th>
                                    <th>Xác nhận bởi</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    payments.forEach(payment => {
                        // Định dạng ngày tháng
                        const paymentDate = new Date(payment.payment_date);
                        const formattedPaymentDate = `${paymentDate.getDate()}/${paymentDate.getMonth() + 1}/${paymentDate.getFullYear()}`;

                        html += `
                            <tr>
                                <td>${formattedPaymentDate}</td>
                                <td>${formatter.format(payment.amount)}</td>
                                <td>${payment.transaction_id || '-'}</td>
                                <td>${payment.admin_username || '-'}</td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;
                }
            }

            // Hiển thị ghi chú nếu có
            if (purchaseOrder.note) {
                html += `
                    <h4>Ghi chú</h4>
                    <p>${purchaseOrder.note}</p>
                `;
            }

            // Cập nhật nội dung modal
            modalContent.innerHTML = html;

            // Đảm bảo các modal khác đều bị ẩn trước khi hiển thị modal mới
            document.querySelectorAll('.modal').forEach(m => {
                m.style.display = 'none';
            });

            // Hiển thị modal
            modal.style.display = 'block';

            // Thêm sự kiện cho các nút cập nhật sản phẩm
            const updateItemButtons = document.querySelectorAll('.update-item-btn');
            updateItemButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const itemId = this.getAttribute('data-id');
                    const orderExchangeRate = parseFloat(this.getAttribute('data-exchange-rate')) || null;
                    showUpdateItemModal(itemId, items, orderExchangeRate);
                });
            });

            // Cập nhật dropdown chọn sản phẩm trong modal thêm tracking
            updateTrackingItemSelect(items);
        }

        // Biến lưu trữ tỷ giá bán của đơn hàng hiện tại
        let currentOrderExchangeRate = null;

        // Hiển thị modal cập nhật thông tin sản phẩm
        function showUpdateItemModal(itemId, items, orderExchangeRate = null) {
            currentItemId = itemId;
            currentOrderExchangeRate = orderExchangeRate;

            // Tìm sản phẩm tương ứng
            const item = items.find(item => item.id == itemId);
            if (!item) return;

            // Lưu thông tin sản phẩm hiện tại để sử dụng trong tính toán
            currentItem = item;

            // Log thông tin để debug
            console.log('Thông tin sản phẩm cần cập nhật:', {
                id: item.id,
                name: item.product_name,
                quantity: item.quantity, // Số lượng cần mua
                price: item.price, // Giá bán($)
                usdExchangeRate: usdExchangeRate, // Tỷ giá USD
                orderExchangeRate: currentOrderExchangeRate // Tỷ giá bán của đơn hàng
            });

            // Đảm bảo các modal khác đều bị ẩn trước khi hiển thị modal mới
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });

            // Cập nhật giá trị trong form
            document.getElementById('purchasedQuantity').value = item.purchased_quantity || 0;

            // Hiển thị số lượng cần mua
            document.getElementById('requiredQuantityDisplay').textContent = `Cần mua: ${item.quantity || 0}`;

            // Cập nhật các trường giá mua mới
            const purchasePriceUSD = item.purchase_price || 0;
            document.getElementById('purchasePriceUSD').value = purchasePriceUSD;

            // Tính giá mua VNĐ từ giá mua USD
            const purchasePriceVND = purchasePriceUSD * (usdExchangeRate || 25000);
            document.getElementById('purchasePriceVND').value = Math.round(purchasePriceVND);

            document.getElementById('purchaseExchangeRate').value = item.purchase_exchange_rate || 0;

            // Reset checkbox và disable tỷ giá mặc định
            document.getElementById('manualRateInput').checked = false;
            document.getElementById('purchaseExchangeRate').disabled = true;

            // Cập nhật dropdown đơn vị tiền tệ
            updateCurrencySelect();

            // Thiết lập sự kiện cho các trường nhập liệu
            setupUpdateItemEvents();

            // Cập nhật hiển thị lãi gross ban đầu
            setTimeout(() => {
                updateGrossProfitDisplay();
            }, 100);

            // Hiển thị modal
            document.getElementById('updateItemModal').style.display = 'block';
        }

        // Cập nhật dropdown đơn vị tiền tệ
        function updateCurrencySelect() {
            const currencySelect = document.getElementById('currencySelect');

            // Xóa tất cả các tùy chọn trừ tùy chọn mặc định
            while (currencySelect.options.length > 1) {
                currencySelect.remove(1);
            }

            // Thêm tất cả tỷ giá từ API
            if (allExchangeRates && allExchangeRates.length > 0) {
                // Nhóm các tỷ giá theo loại
                const vcbRates = allExchangeRates.filter(rate => rate.type === 'vcb');
                const customRates = allExchangeRates.filter(rate => rate.type === 'custom');

                // Thêm nhóm tỷ giá VCB
                if (vcbRates.length > 0) {
                    const vcbGroup = document.createElement('optgroup');
                    vcbGroup.label = 'Tỷ giá Vietcombank';

                    vcbRates.forEach(rate => {
                        const option = document.createElement('option');
                        option.value = rate.currency_code;
                        option.setAttribute('data-rate', rate.rate);
                        option.textContent = `${rate.currency_code} - ${rate.name.split(' - ')[1] || ''}`;
                        vcbGroup.appendChild(option);
                    });

                    currencySelect.appendChild(vcbGroup);
                }

                // Thêm nhóm tỷ giá tùy chỉnh
                if (customRates.length > 0) {
                    const customGroup = document.createElement('optgroup');
                    customGroup.label = 'Tỷ giá tùy chỉnh';

                    customRates.forEach(rate => {
                        const option = document.createElement('option');
                        option.value = rate.currency_code;
                        option.setAttribute('data-rate', rate.rate);
                        option.textContent = rate.name;
                        customGroup.appendChild(option);
                    });

                    currencySelect.appendChild(customGroup);
                }
            }

            // Chọn USD làm mặc định
            currencySelect.value = 'USD';

            // Cập nhật data-rate cho tùy chọn USD
            const usdOption = currencySelect.querySelector('option[value="USD"]');
            if (usdOption && usdExchangeRate) {
                usdOption.setAttribute('data-rate', usdExchangeRate);
            }
        }

        // Thiết lập sự kiện cho các trường nhập liệu trong modal cập nhật thông tin sản phẩm
        function setupUpdateItemEvents() {
            const purchasePriceUSDInput = document.getElementById('purchasePriceUSD');
            const purchasePriceVNDInput = document.getElementById('purchasePriceVND');
            const purchaseExchangeRateInput = document.getElementById('purchaseExchangeRate');
            const manualRateInput = document.getElementById('manualRateInput');
            const currencySelect = document.getElementById('currencySelect');
            const setBoughtQuantityBtn = document.getElementById('setBoughtQuantityBtn');
            const syncPriceBtn = document.getElementById('syncPriceBtn');

            // Lấy tỷ giá hiện tại
            function getCurrentExchangeRate() {
                const selectedOption = currencySelect.options[currencySelect.selectedIndex];
                const rate = selectedOption ? parseFloat(selectedOption.getAttribute('data-rate')) : usdExchangeRate;
                return rate || usdExchangeRate;
            }

            // Tính tỷ giá mua từ giá mua USD
            function calculatePurchaseExchangeRate() {
                if (!manualRateInput.checked && currentItem && currentItem.price > 0) {
                    const purchasePriceUSD = parseFloat(purchasePriceUSDInput.value) || 0;
                    const currentRate = getCurrentExchangeRate();

                    if (purchasePriceUSD > 0) {
                        // Tỷ giá mua = (Giá mua USD ÷ Giá bán USD) × Tỷ giá đơn vị tiền tệ đã chọn
                        const calculatedExchangeRate = (purchasePriceUSD / currentItem.price) * currentRate;

                        if (!isNaN(calculatedExchangeRate) && isFinite(calculatedExchangeRate)) {
                            purchaseExchangeRateInput.value = calculatedExchangeRate.toFixed(2);
                        }
                    }
                }

                // Cập nhật lãi gross real-time
                updateGrossProfitDisplay();
            }

            // Hàm tính toán và hiển thị lãi gross real-time
            function updateGrossProfitDisplay() {
                if (!currentItem) return;

                const purchasedQuantity = parseFloat(document.getElementById('purchasedQuantity').value) || 0;
                const purchasePriceUSD = parseFloat(purchasePriceUSDInput.value) || 0;
                const purchaseExchangeRate = parseFloat(purchaseExchangeRateInput.value) || 0;

                // Hiển thị lãi gross trong modal
                const grossProfitDisplay = document.getElementById('grossProfitDisplay');
                if (!grossProfitDisplay) return;

                // Nếu chưa mua hàng (purchased_quantity = 0), profit = 0
                if (purchasedQuantity === 0) {
                    grossProfitDisplay.textContent = 'Lãi Gross: ₫0 (Chưa mua hàng)';
                    grossProfitDisplay.style.color = '#6c757d'; // Màu xám
                    grossProfitDisplay.style.borderColor = '#e9ecef';

                    console.log('Cập nhật lãi gross: Chưa mua hàng');
                    return;
                }

                // Nếu thiếu thông tin giá mua hoặc tỷ giá, không thể tính toán
                if (purchasePriceUSD === 0 || purchaseExchangeRate === 0) {
                    grossProfitDisplay.textContent = 'Lãi Gross: Chưa thể tính toán (thiếu thông tin giá mua)';
                    grossProfitDisplay.style.color = '#ffc107'; // Màu vàng cảnh báo
                    grossProfitDisplay.style.borderColor = '#ffc107';

                    console.log('Cập nhật lãi gross: Thiếu thông tin giá mua');
                    return;
                }

                // Tính toán theo công thức: Gross Profit = (Selling Price * Actual Quantity * Selling Exchange Rate) - (Purchase Price * Purchased Quantity * Purchase Exchange Rate)
                // Sửa logic: Chỉ tính lãi cho số lượng đã mua thực tế
                const actualQuantityForCalculation = Math.min(purchasedQuantity, currentItem.quantity);
                const sellingTotal = currentItem.price * actualQuantityForCalculation * (currentOrderExchangeRate || 25000);
                const purchaseTotal = purchasePriceUSD * purchasedQuantity * purchaseExchangeRate;
                const grossProfit = sellingTotal - purchaseTotal;

                // Tính phần trăm lợi nhuận
                const profitMargin = purchaseTotal > 0 ? ((grossProfit / purchaseTotal) * 100) : 0;

                // Tạo thông tin chi tiết cho hiển thị
                let profitText = `Lãi Gross: ${formatter.format(grossProfit)} (${profitMargin.toFixed(1)}%)`;

                // Thêm cảnh báo nếu số lượng đã mua khác số lượng cần mua
                if (purchasedQuantity !== currentItem.quantity) {
                    const quantityWarning = ` [Chỉ tính cho ${actualQuantityForCalculation}/${currentItem.quantity} sản phẩm]`;
                    profitText += quantityWarning;
                }

                // Thêm cảnh báo nếu mua với giá cao hơn bán
                if (purchasePriceUSD > currentItem.price) {
                    profitText += ` ⚠️ Giá mua ($${purchasePriceUSD}) > Giá bán ($${currentItem.price})`;
                }

                grossProfitDisplay.textContent = profitText;

                // Đặt màu sắc dựa trên lãi/lỗ
                if (grossProfit > 0) {
                    grossProfitDisplay.style.color = '#28a745'; // Xanh lá - lãi
                    grossProfitDisplay.style.borderColor = '#28a745';
                } else if (grossProfit < 0) {
                    grossProfitDisplay.style.color = '#dc3545'; // Đỏ - lỗ
                    grossProfitDisplay.style.borderColor = '#dc3545';
                } else {
                    grossProfitDisplay.style.color = '#6c757d'; // Xám - hòa vốn
                    grossProfitDisplay.style.borderColor = '#e9ecef';
                }

                console.log('Cập nhật lãi gross:', {
                    purchasedQuantity: purchasedQuantity,
                    purchasePriceUSD: purchasePriceUSD,
                    purchaseExchangeRate: purchaseExchangeRate,
                    sellingTotal: sellingTotal,
                    purchaseTotal: purchaseTotal,
                    grossProfit: grossProfit,
                    profitMargin: profitMargin,
                    currentItem: currentItem
                });
            }

            // Sự kiện cho nút "Đã mua"
            setBoughtQuantityBtn.addEventListener('click', function() {
                if (currentItem && currentItem.quantity) {
                    document.getElementById('purchasedQuantity').value = currentItem.quantity;
                }
            });

            // Sự kiện cho nút "Đồng bộ"
            syncPriceBtn.addEventListener('click', function() {
                if (currentItem && currentItem.price) {
                    // Kiểm tra nếu đang ở chế độ tỷ giá thủ công
                    const isManualRate = manualRateInput.checked;

                    if (isManualRate) {
                        // Hiển thị thông báo xác nhận khi ở chế độ thủ công
                        const confirmMessage = `Bạn đang ở chế độ "Nhập tỷ giá thủ công".\n\n` +
                                             `Nút "Đồng bộ" sẽ:\n` +
                                             `✓ Thay đổi giá mua USD từ $${purchasePriceUSDInput.value || 0} → $${currentItem.price}\n` +
                                             `✓ Tính lại giá mua VNĐ\n` +
                                             `✗ KHÔNG thay đổi tỷ giá mua (vẫn giữ ${purchaseExchangeRateInput.value || 0})\n\n` +
                                             `Bạn có muốn tiếp tục?`;

                        if (!confirm(confirmMessage)) {
                            return; // Hủy bỏ nếu user không đồng ý
                        }
                    }

                    purchasePriceUSDInput.value = currentItem.price;
                    // Tự động tính giá VNĐ và tỷ giá
                    const priceVND = currentItem.price * (usdExchangeRate || 25000);
                    purchasePriceVNDInput.value = Math.round(priceVND);
                    calculatePurchaseExchangeRate();

                    // Hiển thị thông báo thành công
                    if (isManualRate) {
                        console.log('Đã đồng bộ giá mua trong chế độ tỷ giá thủ công');
                    }
                }
            });

            // Sự kiện khi nhập giá mua USD
            purchasePriceUSDInput.addEventListener('input', function() {
                const priceUSD = parseFloat(this.value) || 0;
                if (priceUSD > 0) {
                    // Tính giá VNĐ = Giá USD × Tỷ giá USD
                    const priceVND = priceUSD * (usdExchangeRate || 25000);
                    purchasePriceVNDInput.value = Math.round(priceVND);

                    // Tính tỷ giá mua
                    calculatePurchaseExchangeRate();
                } else {
                    // Cập nhật lãi gross ngay cả khi giá trị = 0
                    updateGrossProfitDisplay();
                }
            });

            // Sự kiện khi nhập giá mua VNĐ
            purchasePriceVNDInput.addEventListener('input', function() {
                const priceVND = parseFloat(this.value) || 0;
                if (priceVND > 0 && usdExchangeRate) {
                    // Tính giá USD = Giá VNĐ ÷ Tỷ giá USD
                    const priceUSD = priceVND / usdExchangeRate;
                    purchasePriceUSDInput.value = priceUSD.toFixed(2);

                    // Tính tỷ giá mua
                    calculatePurchaseExchangeRate();
                } else {
                    // Cập nhật lãi gross ngay cả khi giá trị = 0
                    updateGrossProfitDisplay();
                }
            });

            // Sự kiện khi nhập số lượng đã mua
            document.getElementById('purchasedQuantity').addEventListener('input', function() {
                updateGrossProfitDisplay();
            });

            // Sự kiện khi thay đổi tỷ giá mua thủ công
            purchaseExchangeRateInput.addEventListener('input', function() {
                if (manualRateInput.checked) {
                    // KHÔNG tự động thay đổi giá mua khi nhập tỷ giá thủ công
                    // Chỉ cập nhật hiển thị lãi gross với tỷ giá mới
                    console.log('Tỷ giá mua thủ công được thay đổi:', parseFloat(this.value) || 0);
                    updateGrossProfitDisplay();
                }
            });

            // Sự kiện khi thay đổi checkbox "Nhập thủ công" cho tỷ giá
            manualRateInput.addEventListener('change', function() {
                purchaseExchangeRateInput.disabled = !this.checked;

                // Hiển thị/ẩn cảnh báo
                const warningDiv = document.getElementById('manualRateWarning');
                if (warningDiv) {
                    warningDiv.style.display = this.checked ? 'block' : 'none';
                }

                if (!this.checked) {
                    // Nếu bỏ tick, tính lại tỷ giá tự động
                    calculatePurchaseExchangeRate();
                }
            });

            // Sự kiện khi thay đổi đơn vị tiền tệ
            currencySelect.addEventListener('change', function() {
                calculatePurchaseExchangeRate();
            });
        }

        // Cập nhật dropdown chọn sản phẩm trong modal thêm tracking
        function updateTrackingItemSelect(items) {
            const select = document.getElementById('trackingItemSelect');
            select.innerHTML = '<option value="">Chọn sản phẩm...</option>';

            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.product_name}${item.product_variant ? ` - ${item.product_variant}` : ''}`;
                select.appendChild(option);
            });

            // Khởi tạo lại Select2
            $('#trackingItemSelect').select2({
                width: '100%',
                placeholder: 'Chọn sản phẩm...',
                dropdownParent: $('#addTrackingModal')
            });
        }

        // Cập nhật thông tin sản phẩm
        async function updateOrderItem() {
            if (!currentOrderId || !currentItemId) return;

            const purchasedQuantity = parseInt(document.getElementById('purchasedQuantity').value) || 0;
            const purchasePrice = parseFloat(document.getElementById('purchasePriceUSD').value) || 0; // Sử dụng giá mua USD
            const purchaseExchangeRate = parseFloat(document.getElementById('purchaseExchangeRate').value) || 0;

            showLoader(true, 'Đang cập nhật thông tin sản phẩm...');

            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${currentOrderId}/items/${currentItemId}`, {
                    method: 'PUT',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        purchased_quantity: purchasedQuantity,
                        purchase_price: purchasePrice,
                        purchase_exchange_rate: purchaseExchangeRate
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('Đã cập nhật thông tin sản phẩm thành công!');

                    // Đóng modal
                    document.getElementById('updateItemModal').style.display = 'none';

                    // Tải lại chi tiết đơn mua hộ
                    viewOrderDetail(currentOrderId);

                    // Tải lại danh sách đơn hàng để cập nhật lãi gross trong bảng
                    if (isAdmin) {
                        loadPurchaseOrders();
                    }
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi cập nhật thông tin sản phẩm');
                }
            } catch (error) {
                console.error('Lỗi cập nhật thông tin sản phẩm:', error);
                alert('Có lỗi xảy ra khi cập nhật thông tin sản phẩm. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Thêm tracking mới
        async function addTracking() {
            if (!currentOrderId) return;

            const itemId = document.getElementById('trackingItemSelect').value;
            const trackingNumber = document.getElementById('trackingNumber').value;
            const warehouseId = document.getElementById('warehouseSelect').value;

            if (!itemId) {
                alert('Vui lòng chọn sản phẩm');
                return;
            }

            if (!trackingNumber) {
                alert('Vui lòng nhập tracking number');
                document.getElementById('trackingNumber').focus();
                return;
            }

            if (!warehouseId) {
                alert('Vui lòng chọn kho');
                return;
            }

            showLoader(true, 'Đang thêm tracking...');

            try {
                // Lấy thông tin đơn hàng để biết nhân viên phụ trách
                const orderResponse = await fetch(`${API_URL}/api/admin/purchase-orders/${currentOrderId}`, {
                    headers: getAuthHeader()
                });

                let staffId = null;
                if (orderResponse.ok) {
                    const orderData = await orderResponse.json();
                    staffId = orderData.purchaseOrder.staff_id || currentStaffId;
                }

                // Thêm tracking với thông tin nhân viên phụ trách
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${currentOrderId}/tracking`, {
                    method: 'POST',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        purchase_order_item_id: itemId,
                        tracking_number: trackingNumber,
                        warehouse_id: warehouseId,
                        staff_id: staffId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('Đã thêm tracking thành công!');

                    // Đóng modal
                    document.getElementById('addTrackingModal').style.display = 'none';

                    // Reset form
                    document.getElementById('trackingItemSelect').value = '';
                    document.getElementById('trackingNumber').value = '';
                    document.getElementById('warehouseSelect').value = '';

                    // Tải lại chi tiết đơn mua hộ
                    viewOrderDetail(currentOrderId);
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi thêm tracking');
                }
            } catch (error) {
                console.error('Lỗi thêm tracking:', error);
                alert('Có lỗi xảy ra khi thêm tracking. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Kiểm tra thanh toán đơn mua hộ
        async function checkOrderPayment(orderId) {
            if (!orderId) return;

            showLoader(true, 'Đang kiểm tra giao dịch thanh toán...');

            try {
                // Trước tiên, lấy thông tin đơn hàng hiện tại để kiểm tra trạng thái
                const orderResponse = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}`, {
                    headers: getAuthHeader()
                });

                let shouldCheckPaymentStatus = false;
                let currentOrderData = null;

                if (orderResponse.ok) {
                    try {
                        const orderData = await orderResponse.json();
                        currentOrderData = orderData.purchaseOrder;

                        // Kiểm tra nếu đơn hàng có trạng thái "completed" nhưng còn nợ > 1đ
                        if (currentOrderData && currentOrderData.payment_status === 'completed') {
                            const totalAmount = parseFloat(currentOrderData.total_amount) * parseFloat(currentOrderData.exchange_rate);
                            const paidAmount = parseFloat(currentOrderData.paid_amount) || 0;
                            const remainingAmount = totalAmount - paidAmount;

                            console.log(`Kiểm tra đơn hàng ${orderId}:`, {
                                payment_status: currentOrderData.payment_status,
                                totalAmount: totalAmount,
                                paidAmount: paidAmount,
                                remainingAmount: remainingAmount
                            });

                            if (remainingAmount > 1) {
                                shouldCheckPaymentStatus = true;
                                console.log(`Đơn hàng ${orderId} có trạng thái "completed" nhưng còn nợ ${remainingAmount}đ > 1đ`);
                            }
                        }
                    } catch (parseError) {
                        console.error('Lỗi khi parse thông tin đơn hàng:', parseError);
                        // Tiếp tục với việc kiểm tra thanh toán dù không lấy được thông tin đơn hàng
                    }
                } else {
                    console.warn(`Không thể lấy thông tin đơn hàng ${orderId} (${orderResponse.status}). Tiếp tục kiểm tra thanh toán.`);
                }

                console.log(`Gọi API kiểm tra thanh toán cho đơn hàng ${orderId}...`);
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}/check-payment`, {
                    method: 'POST',
                    headers: getAuthHeader()
                });

                console.log(`API check-payment response:`, {
                    status: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    ok: response.ok
                });

                if (response.ok) {
                    const result = await response.json();

                    // Xử lý logic đặc biệt cho đơn hàng "completed" nhưng còn nợ > 1đ
                    if (shouldCheckPaymentStatus && currentOrderData) {
                        const totalAmount = parseFloat(currentOrderData.total_amount) * parseFloat(currentOrderData.exchange_rate);
                        const paidAmount = parseFloat(currentOrderData.paid_amount) || 0;
                        const remainingAmount = totalAmount - paidAmount;
                        
                        if (remainingAmount > 1) {
                            // Sử dụng API cập nhật đơn hàng thông thường để thay đổi payment_status
                            try {
                                // Tạo payload với thông tin hiện tại của đơn hàng nhưng chỉ thay đổi payment_status
                                const updatePayload = {
                                    user_id: currentOrderData.user_id,
                                    staff_id: currentOrderData.staff_id,
                                    exchange_rate: currentOrderData.exchange_rate,
                                    deposit_percentage: currentOrderData.deposit_percentage,
                                    note: currentOrderData.note ?
                                        `${currentOrderData.note}\n\n[AUTO-UPDATE] Đơn hàng được kiểm tra lại và phát hiện còn nợ ${formatter.format(remainingAmount)}. Chuyển từ "Đã thanh toán" về "Đã cọc" vào ${new Date().toLocaleString('vi-VN')}.` :
                                        `[AUTO-UPDATE] Đơn hàng được kiểm tra lại và phát hiện còn nợ ${formatter.format(remainingAmount)}. Chuyển từ "Đã thanh toán" về "Đã cọc" vào ${new Date().toLocaleString('vi-VN')}.`,
                                    payment_status: 'deposited' // Thay đổi trạng thái thanh toán
                                };

                                const updateResponse = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}`, {
                                    method: 'PUT',
                                    headers: getAuthHeader(),
                                    body: JSON.stringify(updatePayload)
                                });

                                if (updateResponse.ok) {
                                    console.log(`Đã cập nhật trạng thái thanh toán đơn hàng ${orderId} từ "completed" về "deposited"`);
                                    
                                    if (result.newPayments && result.newPayments.length > 0) {
                                        alert(`Đã tìm thấy ${result.newPayments.length} giao dịch thanh toán mới!\n\nĐồng thời đã cập nhật trạng thái đơn hàng từ "Đã thanh toán" về "Đã cọc" do còn nợ ${formatter.format(remainingAmount)}.`);
                                    } else {
                                        alert(`Không có giao dịch thanh toán mới.\n\nĐã cập nhật trạng thái đơn hàng từ "Đã thanh toán" về "Đã cọc" do còn nợ ${formatter.format(remainingAmount)}.`);
                                    }
                                } else {
                                    const updateError = await updateResponse.json();
                                    console.error('Lỗi khi cập nhật trạng thái thanh toán:', updateError);
                                    
                                    // Fallback: Hiển thị thông báo về việc phát hiện sai lệch nhưng không thể tự động sửa
                                    if (result.newPayments && result.newPayments.length > 0) {
                                        alert(`Đã tìm thấy ${result.newPayments.length} giao dịch thanh toán mới!\n\n⚠️ Phát hiện đơn hàng có trạng thái "Đã thanh toán" nhưng còn nợ ${formatter.format(remainingAmount)}. Vui lòng kiểm tra và cập nhật thủ công.`);
                                    } else {
                                        alert(`Không có giao dịch thanh toán mới.\n\n⚠️ Phát hiện đơn hàng có trạng thái "Đã thanh toán" nhưng còn nợ ${formatter.format(remainingAmount)}. Vui lòng kiểm tra và cập nhật thủ công.`);
                                    }
                                }
                            } catch (updateError) {
                                console.error('Lỗi khi cập nhật trạng thái thanh toán:', updateError);
                                
                                // Fallback: Hiển thị thông báo về việc phát hiện sai lệch
                                if (result.newPayments && result.newPayments.length > 0) {
                                    alert(`Đã tìm thấy ${result.newPayments.length} giao dịch thanh toán mới!\n\n⚠️ Phát hiện đơn hàng có trạng thái "Đã thanh toán" nhưng còn nợ ${formatter.format(remainingAmount)}. Vui lòng kiểm tra và cập nhật thủ công.`);
                                } else {
                                    alert(`Không có giao dịch thanh toán mới.\n\n⚠️ Phát hiện đơn hàng có trạng thái "Đã thanh toán" nhưng còn nợ ${formatter.format(remainingAmount)}. Vui lòng kiểm tra và cập nhật thủ công.`);
                                }
                            }
                        } else {
                            // Trường hợp còn nợ <= 1đ, giữ nguyên logic cũ
                            if (result.newPayments && result.newPayments.length > 0) {
                                alert(`Đã tìm thấy ${result.newPayments.length} giao dịch thanh toán mới!`);
                            } else {
                                alert(result.message || 'Không có giao dịch thanh toán mới');
                            }
                        }
                    } else {
                        // Logic bình thường cho các đơn hàng khác
                        if (result.newPayments && result.newPayments.length > 0) {
                            alert(`Đã tìm thấy ${result.newPayments.length} giao dịch thanh toán mới!`);
                        } else {
                            alert(result.message || 'Không có giao dịch thanh toán mới');
                        }
                    }

                    // Tải lại danh sách đơn mua hộ
                    loadPurchaseOrders();

                    // Nếu đang xem chi tiết đơn, tải lại chi tiết
                    if (currentOrderId === orderId) {
                        // Đóng modal hiện tại
                        document.getElementById('orderDetailModal').style.display = 'none';

                        // Đợi một chút để đảm bảo API đã cập nhật dữ liệu
                        setTimeout(() => {
                            // Mở lại modal với dữ liệu mới
                            viewOrderDetail(orderId);
                        }, 500);
                    }
                } else {
                    // Kiểm tra content-type để xử lý response phù hợp
                    const contentType = response.headers.get('content-type');
                    let errorMessage = 'Có lỗi xảy ra khi kiểm tra thanh toán';

                    try {
                        if (contentType && contentType.includes('application/json')) {
                            // Nếu response là JSON, parse và lấy message
                            const error = await response.json();
                            errorMessage = error.message || errorMessage;
                        } else {
                            // Nếu response không phải JSON (có thể là HTML), lấy text
                            const errorText = await response.text();
                            console.error('Server trả về lỗi không phải JSON:', errorText);

                            // Nếu là HTML error page, hiển thị thông báo chung
                            if (errorText.includes('<!DOCTYPE') || errorText.includes('<html')) {
                                errorMessage = `Lỗi server (${response.status}): Không thể xử lý yêu cầu kiểm tra thanh toán. Vui lòng thử lại sau hoặc liên hệ admin.`;
                            } else {
                                errorMessage = errorText || errorMessage;
                            }
                        }
                    } catch (parseError) {
                        console.error('Lỗi khi parse response:', parseError);
                        errorMessage = `Lỗi server (${response.status}): Không thể xử lý phản hồi từ server. Vui lòng thử lại sau.`;
                    }

                    alert(errorMessage);
                }
            } catch (error) {
                console.error('Lỗi kiểm tra thanh toán:', error);

                // Xử lý các loại lỗi khác nhau
                let errorMessage = 'Có lỗi xảy ra khi kiểm tra thanh toán. Vui lòng thử lại sau.';

                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng và thử lại.';
                } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
                    errorMessage = 'Server trả về dữ liệu không hợp lệ. Vui lòng thử lại sau hoặc liên hệ admin.';
                } else if (error.message) {
                    errorMessage = `Lỗi: ${error.message}`;
                }

                alert(errorMessage);
            } finally {
                showLoader(false);
            }
        }

        // Xác nhận thanh toán thủ công
        async function confirmManualPayment() {
            const amount = document.getElementById('manualPaymentAmount').value;
            const paymentDate = document.getElementById('manualPaymentDate').value;
            const note = document.getElementById('manualPaymentNote').value;

            // Validation
            if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
                alert('Vui lòng nhập số tiền hợp lệ (lớn hơn 0)');
                return;
            }

            if (!paymentDate) {
                alert('Vui lòng chọn ngày nhận tiền');
                return;
            }

            // Xác nhận từ người dùng
            const confirmMessage = `Bạn có chắc chắn muốn xác nhận thanh toán thủ công với thông tin sau?\n\n` +
                                 `Số tiền: ${formatter.format(parseFloat(amount))}\n` +
                                 `Ngày nhận: ${paymentDate}\n` +
                                 `Ghi chú: ${note || 'Không có'}\n\n` +
                                 `Thông tin này sẽ được lưu vào hệ thống và không thể hoàn tác.`;

            if (!confirm(confirmMessage)) {
                return;
            }

            showLoader(true, 'Đang xác nhận thanh toán...');

            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${currentOrderId}/manual-payment`, {
                    method: 'POST',
                    headers: {
                        ...getAuthHeader(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        amount: parseFloat(amount),
                        payment_date: paymentDate,
                        note: note || null
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('Đã xác nhận thanh toán thủ công thành công!');

                    // Đóng modal
                    document.getElementById('manualPaymentModal').style.display = 'none';

                    // Reset form
                    document.getElementById('manualPaymentAmount').value = '';
                    document.getElementById('manualPaymentDate').value = '';
                    document.getElementById('manualPaymentNote').value = '';

                    // Tải lại danh sách đơn mua hộ
                    loadPurchaseOrders();

                    // Nếu đang xem chi tiết đơn, tải lại chi tiết
                    if (currentOrderId) {
                        // Đóng modal chi tiết hiện tại
                        document.getElementById('orderDetailModal').style.display = 'none';

                        // Đợi một chút để đảm bảo API đã cập nhật dữ liệu
                        setTimeout(() => {
                            // Mở lại modal với dữ liệu mới
                            viewOrderDetail(currentOrderId);
                        }, 500);
                    }
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi xác nhận thanh toán thủ công');
                }
            } catch (error) {
                console.error('Lỗi xác nhận thanh toán thủ công:', error);
                alert('Có lỗi xảy ra khi xác nhận thanh toán thủ công. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Kiểm tra trạng thái tracking
        async function checkTrackingStatus(trackingNumber) {
            if (!trackingNumber) {
                alert('Vui lòng nhập tracking number');
                return null;
            }

            showLoader(true, 'Đang lấy thông tin tracking...');

            try {
                const response = await fetch(`${API_URL}/api/tracking/${trackingNumber}`, {
                    headers: getAuthHeader()
                });

                if (response.ok) {
                    const result = await response.json();
                    return result;
                } else {
                    const error = await response.json();
                    console.error('Lỗi kiểm tra tracking:', error);
                    return null;
                }
            } catch (error) {
                console.error('Lỗi kiểm tra tracking:', error);
                return null;
            } finally {
                showLoader(false);
            }
        }

        // Cập nhật trạng thái đơn mua hộ dựa trên tracking
        async function updateOrderStatusFromTracking(orderId, trackingNumber) {
            const trackingInfo = await checkTrackingStatus(trackingNumber);

            if (!trackingInfo) {
                alert('Không thể lấy thông tin tracking');
                return;
            }

            showLoader(true, 'Đang cập nhật trạng thái đơn hàng...');

            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}/update-status`, {
                    method: 'PUT',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        tracking_number: trackingNumber,
                        tracking_status: trackingInfo.status
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(result.message || 'Đã cập nhật trạng thái đơn mua hộ thành công!');

                    // Tải lại danh sách đơn mua hộ
                    loadPurchaseOrders();

                    // Tải lại chi tiết đơn mua hộ nếu đang xem
                    if (currentOrderId === orderId) {
                        viewOrderDetail(orderId);
                    }
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi cập nhật trạng thái đơn mua hộ');
                }
            } catch (error) {
                console.error('Lỗi cập nhật trạng thái đơn mua hộ:', error);
                alert('Có lỗi xảy ra khi cập nhật trạng thái đơn mua hộ. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Xóa đơn mua hộ (chỉ dành cho admin)
        async function deleteOrder(orderId) {
            if (!isAdmin) {
                alert('Bạn không có quyền xóa đơn mua hộ');
                return;
            }

            showLoader(true, 'Đang xóa đơn hàng...');

            try {
                // Sử dụng phương thức POST với endpoint /delete
                console.log(`Đang gửi yêu cầu xóa đơn mua hộ với ID: ${orderId}`);

                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}/delete`, {
                    method: 'POST',
                    headers: {
                        ...getAuthHeader(),
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                if (response.ok) {
                    const result = await response.json();

                    // Đóng modal chi tiết nếu đang xem đơn bị xóa
                    if (currentOrderId === orderId) {
                        document.getElementById('orderDetailModal').style.display = 'none';
                        currentOrderId = null;
                    }

                    // Xóa dòng khỏi bảng ngay lập tức để UX tốt hơn
                    const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
                    if (orderRow) {
                        orderRow.remove();
                    }

                    // Tải lại danh sách đơn mua hộ sau một khoảng delay ngắn
                    setTimeout(() => {
                        loadPurchaseOrders();
                    }, 500);

                    alert('Đã xóa đơn mua hộ thành công!');
                } else {
                    // Kiểm tra nếu có lỗi JSON
                    try {
                        const error = await response.json();
                        alert(error.message || 'Có lỗi xảy ra khi xóa đơn mua hộ');
                    } catch (jsonError) {
                        // Nếu không phải JSON, hiển thị thông báo lỗi chung
                        alert(`Có lỗi xảy ra khi xóa đơn mua hộ: ${response.status} ${response.statusText}`);
                    }
                }
            } catch (error) {
                console.error('Lỗi xóa đơn mua hộ:', error);
                alert('Có lỗi xảy ra khi xóa đơn mua hộ. Vui lòng thử lại sau.');
            } finally {
                showLoader(false);
            }
        }

        // Hàm chỉnh sửa đơn hàng
        async function editOrder(orderId) {
            if (!isAdmin) {
                alert('Bạn không có quyền chỉnh sửa đơn hàng');
                return;
            }

            currentEditOrderId = orderId;
            showLoader(true, 'Đang tải thông tin đơn hàng...');

            try {
                // Tải chi tiết đơn hàng
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${orderId}`, {
                    headers: getAuthHeader()
                });

                if (response.ok) {
                    const data = await response.json();
                    populateEditOrderModal(data);
                    document.getElementById('editOrderModal').style.display = 'block';
                } else {
                    alert('Không thể tải thông tin đơn hàng');
                }
            } catch (error) {
                console.error('Lỗi khi tải thông tin đơn hàng:', error);
                alert('Có lỗi xảy ra khi tải thông tin đơn hàng');
            } finally {
                showLoader(false);
            }
        }

        // Điền dữ liệu vào modal chỉnh sửa
        function populateEditOrderModal(data) {
            const { purchaseOrder, items } = data;

            // Điền dữ liệu vào dropdown khách hàng
            const editUserSelect = document.getElementById('editUserSelect');
            editUserSelect.innerHTML = '<option value="">Chọn khách hàng</option>';
            allUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.username} (${user.staff_name || 'Chưa phân công'})`;
                editUserSelect.appendChild(option);
            });

            // Điền dữ liệu vào dropdown nhân viên
            const editStaffSelect = document.getElementById('editStaffSelect');
            editStaffSelect.innerHTML = '<option value="">Chọn nhân viên</option>';
            allStaff.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = staff.fullname;
                editStaffSelect.appendChild(option);
            });

            // Điền thông tin cơ bản
            document.getElementById('editUserSelect').value = purchaseOrder.user_id;
            document.getElementById('editStaffSelect').value = purchaseOrder.staff_id || '';
            document.getElementById('editExchangeRate').value = purchaseOrder.exchange_rate;
            document.getElementById('editDepositPercentage').value = purchaseOrder.deposit_percentage;
            document.getElementById('editNote').value = purchaseOrder.note || '';

            // Điền danh sách sản phẩm
            editOrderItems = items.map(item => ({
                id: item.id,
                product_link: item.product_link || '',
                product_name: item.product_name,
                product_variant: item.product_variant || '',
                quantity: item.quantity,
                price: item.price,
                purchased_quantity: item.purchased_quantity || 0,
                purchase_price: item.purchase_price || 0,
                purchase_exchange_rate: item.purchase_exchange_rate || 0
            }));

            // Đảm bảo luôn có một dòng trống ở cuối để người dùng có thể thêm sản phẩm mới
            if (editOrderItems.length === 0) {
                // Nếu không có sản phẩm nào, thêm một dòng trống
                editOrderItems.push({
                    product_link: '',
                    product_name: '',
                    product_variant: '',
                    quantity: 1,
                    price: 0,
                    purchased_quantity: 0,
                    purchase_price: 0,
                    purchase_exchange_rate: 0
                });
            } else {
                // Kiểm tra dòng cuối cùng có trống không
                const lastItem = editOrderItems[editOrderItems.length - 1];
                const isLastItemEmpty = (!lastItem.product_name || lastItem.product_name.trim() === '') &&
                                       (!lastItem.product_variant || lastItem.product_variant.trim() === '') &&
                                       (!lastItem.product_link || lastItem.product_link.trim() === '') &&
                                       (lastItem.quantity <= 1) &&
                                       (lastItem.price <= 0);

                // Nếu dòng cuối không trống, thêm một dòng trống mới
                if (!isLastItemEmpty) {
                    editOrderItems.push({
                        product_link: '',
                        product_name: '',
                        product_variant: '',
                        quantity: 1,
                        price: 0,
                        purchased_quantity: 0,
                        purchase_price: 0,
                        purchase_exchange_rate: 0
                    });
                }
            }

            updateEditItemsTable();

            // Khởi tạo Select2 cho các dropdown
            $('#editUserSelect').select2({
                width: '100%',
                placeholder: 'Chọn khách hàng'
            });

            $('#editStaffSelect').select2({
                width: '100%',
                placeholder: 'Chọn nhân viên'
            });
        }

        // Cập nhật bảng sản phẩm trong modal chỉnh sửa
        function updateEditItemsTable() {
            const tbody = document.getElementById('editItemsTableBody');
            tbody.innerHTML = '';

            editOrderItems.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="url" class="edit-product-link" data-index="${index}" value="${item.product_link}" placeholder="https://...">
                    </td>
                    <td>
                        <input type="text" class="edit-product-name" data-index="${index}" value="${item.product_name}" required>
                    </td>
                    <td>
                        <input type="text" class="edit-product-variant" data-index="${index}" value="${item.product_variant}" placeholder="Màu sắc, kích thước...">
                    </td>
                    <td>
                        <input type="number" class="edit-product-quantity" data-index="${index}" value="${item.quantity}" min="1" required>
                    </td>
                    <td>
                        <input type="number" class="edit-product-price" data-index="${index}" value="${item.price}" step="0.01" min="0" required>
                    </td>
                    <td>
                        <button type="button" class="remove-edit-row-btn" data-index="${index}">X</button>
                    </td>
                `;

                tbody.appendChild(row);

                // Thêm event listeners cho các input
                const inputs = row.querySelectorAll('input');
                inputs.forEach(input => {
                    // Event listener cho input (real-time sync)
                    input.addEventListener('input', function() {
                        const idx = parseInt(this.getAttribute('data-index'));
                        const field = this.classList[0].replace('edit-product-', '');
                        const value = this.value;

                        if (idx >= 0 && idx < editOrderItems.length) {
                            if (field === 'quantity') {
                                editOrderItems[idx][field] = parseFloat(value) || 1;
                            } else if (field === 'price') {
                                editOrderItems[idx][field] = parseFloat(value) || 0;
                            } else {
                                editOrderItems[idx][field] = value;
                            }
                        }
                    });

                    // Event listener cho blur để đảm bảo dữ liệu được lưu khi rời khỏi input
                    input.addEventListener('blur', function() {
                        const idx = parseInt(this.getAttribute('data-index'));
                        const field = this.classList[0].replace('edit-product-', '');
                        const value = this.value;

                        if (idx >= 0 && idx < editOrderItems.length) {
                            if (field === 'quantity') {
                                editOrderItems[idx][field] = parseFloat(value) || 1;
                            } else if (field === 'price') {
                                editOrderItems[idx][field] = parseFloat(value) || 0;
                            } else {
                                editOrderItems[idx][field] = value;
                            }
                        }
                    });
                });

                // Event listener cho nút xóa
                const removeBtn = row.querySelector('.remove-edit-row-btn');
                removeBtn.addEventListener('click', function() {
                    // Đồng bộ dữ liệu trước khi xóa
                    syncEditItemsFromInputs();

                    const idx = parseInt(this.getAttribute('data-index'));
                    editOrderItems.splice(idx, 1);
                    updateEditItemsTable();
                });
            });
        }

        // Đồng bộ dữ liệu từ các input vào mảng editOrderItems
        function syncEditItemsFromInputs() {
            console.log('Đồng bộ dữ liệu từ input vào mảng editOrderItems...');
            const rows = document.querySelectorAll('#editItemsTableBody tr');
            console.log(`Tìm thấy ${rows.length} dòng, editOrderItems có ${editOrderItems.length} phần tử`);

            rows.forEach((row, index) => {
                if (index < editOrderItems.length) {
                    const oldData = { ...editOrderItems[index] };

                    // Lấy dữ liệu từ từng input cụ thể
                    const linkInput = row.querySelector('.edit-product-link');
                    const nameInput = row.querySelector('.edit-product-name');
                    const variantInput = row.querySelector('.edit-product-variant');
                    const quantityInput = row.querySelector('.edit-product-quantity');
                    const priceInput = row.querySelector('.edit-product-price');

                    console.log(`Dòng ${index + 1} - Input values:`, {
                        link: linkInput ? linkInput.value : 'không tìm thấy',
                        name: nameInput ? nameInput.value : 'không tìm thấy',
                        variant: variantInput ? variantInput.value : 'không tìm thấy',
                        quantity: quantityInput ? quantityInput.value : 'không tìm thấy',
                        price: priceInput ? priceInput.value : 'không tìm thấy'
                    });

                    // Cập nhật dữ liệu
                    if (linkInput) editOrderItems[index].product_link = linkInput.value;
                    if (nameInput) editOrderItems[index].product_name = nameInput.value;
                    if (variantInput) editOrderItems[index].product_variant = variantInput.value;
                    if (quantityInput) editOrderItems[index].quantity = parseFloat(quantityInput.value) || 1;
                    if (priceInput) editOrderItems[index].price = parseFloat(priceInput.value) || 0;

                    // Log thay đổi nếu có
                    const hasChanges = JSON.stringify(oldData) !== JSON.stringify(editOrderItems[index]);
                    if (hasChanges) {
                        console.log(`Dòng ${index + 1} đã thay đổi:`, {
                            cũ: oldData,
                            mới: editOrderItems[index]
                        });
                    }
                }
            });

            console.log('Dữ liệu editOrderItems sau khi đồng bộ:', editOrderItems);
        }

        // Thêm một dòng mới vào cuối bảng mà không render lại toàn bộ
        function addNewRowToTable(item, index) {
            const tbody = document.getElementById('editItemsTableBody');
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><input type="text" class="edit-product-link" data-index="${index}" value="${item.product_link || ''}" placeholder="Link sản phẩm"></td>
                <td><input type="text" class="edit-product-name" data-index="${index}" value="${item.product_name || ''}" placeholder="Tên sản phẩm" required></td>
                <td><input type="text" class="edit-product-variant" data-index="${index}" value="${item.product_variant || ''}" placeholder="Phân loại"></td>
                <td><input type="number" class="edit-product-quantity" data-index="${index}" value="${item.quantity || 1}" min="1" required></td>
                <td><input type="number" class="edit-product-price" data-index="${index}" value="${item.price || 0}" step="0.01" min="0" required></td>
                <td><button type="button" class="remove-edit-row-btn" data-index="${index}">Xóa</button></td>
            `;

            tbody.appendChild(row);

            // Thêm event listeners cho dòng mới
            const inputs = row.querySelectorAll('input');
            inputs.forEach(input => {
                // Event listener cho input (real-time sync)
                input.addEventListener('input', function() {
                    const idx = parseInt(this.getAttribute('data-index'));
                    const field = this.classList[0].replace('edit-product-', '');
                    const value = this.value;

                    if (idx >= 0 && idx < editOrderItems.length) {
                        if (field === 'quantity') {
                            editOrderItems[idx][field] = parseFloat(value) || 1;
                        } else if (field === 'price') {
                            editOrderItems[idx][field] = parseFloat(value) || 0;
                        } else {
                            editOrderItems[idx][field] = value;
                        }
                    }
                });

                // Event listener cho blur để đảm bảo dữ liệu được lưu khi rời khỏi input
                input.addEventListener('blur', function() {
                    const idx = parseInt(this.getAttribute('data-index'));
                    const field = this.classList[0].replace('edit-product-', '');
                    const value = this.value;

                    if (idx >= 0 && idx < editOrderItems.length) {
                        if (field === 'quantity') {
                            editOrderItems[idx][field] = parseFloat(value) || 1;
                        } else if (field === 'price') {
                            editOrderItems[idx][field] = parseFloat(value) || 0;
                        } else {
                            editOrderItems[idx][field] = value;
                        }
                    }
                });
            });

            // Event listener cho nút xóa
            const removeBtn = row.querySelector('.remove-edit-row-btn');
            removeBtn.addEventListener('click', function() {
                // Đồng bộ dữ liệu trước khi xóa
                syncEditItemsFromInputs();

                const idx = parseInt(this.getAttribute('data-index'));
                editOrderItems.splice(idx, 1);
                updateEditItemsTable(); // Phải render lại toàn bộ khi xóa để cập nhật index
            });
        }

        // Thêm sản phẩm mới trong modal chỉnh sửa
        function addEditItem() {
            console.log('=== BẮT ĐẦU THÊM DÒNG MỚI ===');
            console.log('editOrderItems trước khi đồng bộ:', JSON.parse(JSON.stringify(editOrderItems)));

            // Đồng bộ dữ liệu hiện tại trước khi thêm dòng mới
            syncEditItemsFromInputs();

            console.log('editOrderItems sau khi đồng bộ:', JSON.parse(JSON.stringify(editOrderItems)));

            // Thêm dòng mới vào mảng
            const newItem = {
                product_link: '',
                product_name: '',
                product_variant: '',
                quantity: 1,
                price: 0,
                purchased_quantity: 0,
                purchase_price: 0,
                purchase_exchange_rate: 0
            };
            editOrderItems.push(newItem);

            console.log('editOrderItems sau khi thêm dòng mới:', JSON.parse(JSON.stringify(editOrderItems)));

            // Thêm dòng mới vào bảng mà không render lại toàn bộ
            addNewRowToTable(newItem, editOrderItems.length - 1);

            console.log('=== KẾT THÚC THÊM DÒNG MỚI ===');
        }

        // Lưu thay đổi đơn hàng
        async function saveEditOrder() {
            if (!currentEditOrderId) {
                alert('Không tìm thấy ID đơn hàng');
                return;
            }

            // Đồng bộ dữ liệu từ input trước khi validate và lưu
            syncEditItemsFromInputs();

            // Validate dữ liệu
            const userId = document.getElementById('editUserSelect').value;
            const staffId = document.getElementById('editStaffSelect').value;
            const exchangeRate = parseFloat(document.getElementById('editExchangeRate').value);
            const depositPercentage = parseFloat(document.getElementById('editDepositPercentage').value);
            const note = document.getElementById('editNote').value;

            if (!userId || !exchangeRate || !depositPercentage) {
                alert('Vui lòng điền đầy đủ thông tin bắt buộc');
                return;
            }

            // Validate sản phẩm - lọc ra các sản phẩm hợp lệ
            const validItems = editOrderItems.filter(item => {
                return item.product_name &&
                       item.product_name.trim() !== '' &&
                       item.quantity > 0 &&
                       item.price > 0;
            });

            if (validItems.length === 0) {
                alert('Vui lòng thêm ít nhất một sản phẩm hợp lệ với đầy đủ thông tin (tên, số lượng > 0, giá > 0)');
                return;
            }

            // Kiểm tra các sản phẩm không hợp lệ và hiển thị lỗi cụ thể
            const invalidItems = [];
            editOrderItems.forEach((item, index) => {
                // Bỏ qua dòng trống hoàn toàn
                const isEmpty = (!item.product_name || item.product_name.trim() === '') &&
                               (!item.product_variant || item.product_variant.trim() === '') &&
                               (!item.product_link || item.product_link.trim() === '') &&
                               (item.quantity <= 1) &&
                               (item.price <= 0);

                if (!isEmpty) {
                    // Nếu không phải dòng trống, kiểm tra tính hợp lệ
                    if (!item.product_name || item.product_name.trim() === '') {
                        invalidItems.push(`Sản phẩm ${index + 1}: Thiếu tên sản phẩm`);
                    } else if (item.quantity <= 0) {
                        invalidItems.push(`Sản phẩm ${index + 1}: Số lượng phải lớn hơn 0`);
                    } else if (item.price <= 0) {
                        invalidItems.push(`Sản phẩm ${index + 1}: Giá phải lớn hơn 0`);
                    }
                }
            });

            if (invalidItems.length > 0) {
                alert('Có lỗi trong danh sách sản phẩm:\n' + invalidItems.join('\n'));
                return;
            }

            showLoader(true, 'Đang lưu thay đổi...');

            try {
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${currentEditOrderId}`, {
                    method: 'PUT',
                    headers: getAuthHeader(),
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        staff_id: staffId ? parseInt(staffId) : null,
                        exchange_rate: exchangeRate,
                        deposit_percentage: depositPercentage,
                        note: note,
                        items: validItems.map(item => ({
                            id: item.id, // Bao gồm ID để backend có thể phân biệt update vs insert
                            product_link: item.product_link,
                            product_name: item.product_name,
                            product_variant: item.product_variant,
                            quantity: item.quantity,
                            price: item.price,
                            purchased_quantity: item.purchased_quantity || 0,
                            purchase_price: item.purchase_price || 0,
                            purchase_exchange_rate: item.purchase_exchange_rate || 0
                        }))
                    })
                });

                if (response.ok) {
                    // Kiểm tra và cập nhật trạng thái thanh toán sau khi sửa đơn
                    const newTotalAmount = exchangeRate * validItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
                    const currentPaidAmount = parseFloat(document.getElementById('editOrderModal').dataset.currentPaidAmount) || 0;
                    const currentPaymentStatus = document.getElementById('editOrderModal').dataset.currentPaymentStatus || 'pending';
                    
                    const statusCheck = checkAndUpdatePaymentStatusAfterEdit(
                        currentEditOrderId,
                        newTotalAmount,
                        currentPaidAmount,
                        currentPaymentStatus
                    );
                    
                    if (statusCheck.shouldUpdate) {
                        console.log('Cần cập nhật trạng thái thanh toán:', statusCheck.reason);
                        // Gọi API để cập nhật trạng thái thanh toán
                        await updatePaymentStatusAfterEdit(currentEditOrderId, statusCheck.newStatus, statusCheck.reason);
                    }
                    
                    alert('Đã cập nhật đơn hàng thành công!');
                    document.getElementById('editOrderModal').style.display = 'none';
                    loadPurchaseOrders(); // Tải lại danh sách đơn hàng
                } else {
                    const error = await response.json();
                    alert(error.message || 'Có lỗi xảy ra khi cập nhật đơn hàng');
                }
            } catch (error) {
                console.error('Lỗi khi cập nhật đơn hàng:', error);
                alert('Có lỗi xảy ra khi cập nhật đơn hàng');
            } finally {
                showLoader(false);
            }
        }

        // Debug function for admin table enhancements
        function debugAdminTableFeatures() {
            console.log('🔍 Debugging admin table features...');

            // Check if elements exist
            const elements = {
                adminDetailSection: document.getElementById('adminDetailSection'),
                adminOrdersTable: document.getElementById('adminOrdersTable'),
                showAllColumnsBtn: document.getElementById('showAllColumnsBtn'),
                fullscreenBtn: document.getElementById('fullscreenBtn'),
                ordersTable: document.getElementById('ordersTable')
            };

            console.log('🔍 Element check:', elements);

            // Check if initialization flag is set
            console.log('🔍 Initialization status:', {
                adminTableInitialized: window.adminTableInitialized,
                isAdmin: isAdmin
            });

            // Check localStorage
            const saved = localStorage.getItem('adminTable_preferences');
            console.log('🔍 Saved preferences:', saved ? JSON.parse(saved) : 'None');

            // Check event listeners by trying to trigger them
            if (elements.showAllColumnsBtn) {
                console.log('🔍 Show all columns button found and ready');
            }

            if (elements.fullscreenBtn) {
                console.log('🔍 Testing fullscreen button...');
                // Don't actually click to avoid disruption
                console.log('Fullscreen button found and ready');
            }

            // Check column resizers
            const resizers = document.querySelectorAll('.column-resizer');
            console.log(`🔍 Found ${resizers.length} column resizers`);

            return {
                elements,
                resizers: resizers.length,
                initialized: window.adminTableInitialized,
                preferences: saved ? JSON.parse(saved) : null
            };
        }

        // Make debug function available globally for console testing
        window.debugAdminTableFeatures = debugAdminTableFeatures;

        // Auto-run debug if in development mode (check for localhost)
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            // Run debug after a delay to ensure everything is loaded
            setTimeout(() => {
                console.log('🚀 Auto-running debug for localhost...');
                debugAdminTableFeatures();
            }, 2000);
        }
    </script>
</body>
</html>
