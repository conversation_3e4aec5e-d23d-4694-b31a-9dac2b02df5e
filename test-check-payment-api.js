const axios = require('axios');
require('dotenv').config();

// Cấu hình API
const API_URL = process.env.API_URL || 'https://trustmove.vn';
const ADMIN_TOKEN = process.env.ADMIN_TOKEN; // Cần có token admin để test

/**
 * Test API check payment cho đơn hàng
 */
async function testCheckPaymentAPI() {
    console.log('=== TEST API CHECK PAYMENT ===');
    console.log(`API URL: ${API_URL}`);
    
    if (!ADMIN_TOKEN) {
        console.error('❌ Thiếu ADMIN_TOKEN trong file .env');
        console.log('Vui lòng thêm ADMIN_TOKEN=your_admin_token vào file .env');
        return;
    }
    
    // Test với đơn hàng ID 156 (từ lỗi báo cáo)
    const orderId = 156;
    
    try {
        console.log(`\n🔍 Kiểm tra đơn hàng ${orderId}...`);
        
        // 1. <PERSON><PERSON><PERSON> tra thông tin đơn hàng trước
        console.log('\n1. Lấy thông tin đơn hàng...');
        const orderResponse = await axios.get(`${API_URL}/api/admin/purchase-orders/${orderId}`, {
            headers: {
                'Authorization': `Bearer ${ADMIN_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Thông tin đơn hàng:', {
            id: orderResponse.data.purchaseOrder.id,
            payment_status: orderResponse.data.purchaseOrder.payment_status,
            total_amount: orderResponse.data.purchaseOrder.total_amount,
            paid_amount: orderResponse.data.purchaseOrder.paid_amount,
            exchange_rate: orderResponse.data.purchaseOrder.exchange_rate
        });
        
        // 2. Test API check payment
        console.log('\n2. Gọi API check payment...');
        const checkPaymentResponse = await axios.post(`${API_URL}/api/admin/purchase-orders/${orderId}/check-payment`, {}, {
            headers: {
                'Authorization': `Bearer ${ADMIN_TOKEN}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 seconds timeout
        });
        
        console.log('✅ API check payment thành công:', {
            status: checkPaymentResponse.status,
            message: checkPaymentResponse.data.message,
            newPayments: checkPaymentResponse.data.newPayments?.length || 0
        });
        
    } catch (error) {
        console.error('\n❌ Lỗi khi test API:');
        
        if (error.response) {
            // Server trả về response với status code lỗi
            console.error('Response Status:', error.response.status);
            console.error('Response Headers:', error.response.headers);
            
            // Kiểm tra content-type của response
            const contentType = error.response.headers['content-type'];
            console.error('Content-Type:', contentType);
            
            if (contentType && contentType.includes('application/json')) {
                console.error('Response Data (JSON):', error.response.data);
            } else {
                console.error('Response Data (Text):', error.response.data);
                
                // Nếu là HTML, chỉ hiển thị một phần để debug
                if (typeof error.response.data === 'string' && error.response.data.includes('<!DOCTYPE')) {
                    console.error('Response là HTML error page. Đầu 500 ký tự:');
                    console.error(error.response.data.substring(0, 500) + '...');
                }
            }
        } else if (error.request) {
            // Request được gửi nhưng không nhận được response
            console.error('Không nhận được response từ server');
            console.error('Request config:', error.config);
        } else {
            // Lỗi khác
            console.error('Error Message:', error.message);
        }
    }
}

/**
 * Test với nhiều đơn hàng khác nhau
 */
async function testMultipleOrders() {
    console.log('\n=== TEST MULTIPLE ORDERS ===');
    
    const orderIds = [156, 155, 154]; // Test với một vài đơn hàng
    
    for (const orderId of orderIds) {
        console.log(`\n--- Test đơn hàng ${orderId} ---`);
        
        try {
            const response = await axios.post(`${API_URL}/api/admin/purchase-orders/${orderId}/check-payment`, {}, {
                headers: {
                    'Authorization': `Bearer ${ADMIN_TOKEN}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            
            console.log(`✅ Đơn ${orderId}: ${response.data.message}`);
            
        } catch (error) {
            console.error(`❌ Đơn ${orderId}: ${error.response?.status || error.message}`);
        }
    }
}

// Chạy test
async function runTests() {
    await testCheckPaymentAPI();
    
    if (ADMIN_TOKEN) {
        await testMultipleOrders();
    }
    
    console.log('\n=== TEST HOÀN TẤT ===');
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testCheckPaymentAPI,
    testMultipleOrders
};
