const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const readline = require('readline');
require('dotenv').config();

const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'tracking_db',
    password: 'England1@',
    port: 5432,
});

async function createAdmin(username, password) {
    try {
        // Mã hóa mật khẩu
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(password, salt);

        // Kiểm tra xem username đã tồn tại chưa
        const checkQuery = 'SELECT * FROM admin_users WHERE username = $1';
        const checkResult = await pool.query(checkQuery, [username]);

        if (checkResult.rows.length > 0) {
            throw new Error('Username này đã tồn tại!');
        }

        // Thêm tài khoản admin mới
        const insertQuery = 'INSERT INTO admin_users (username, password_hash) VALUES ($1, $2) RETURNING id';
        const result = await pool.query(insertQuery, [username, passwordHash]);

        return result;
    } catch (error) {
        throw error;
    }
}

// Tạo interface cho việc đọc input từ command line
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

rl.question('Nhập username cho tài khoản admin: ', (username) => {
    rl.question('Nhập mật khẩu cho tài khoản admin: ', async (password) => {
        try {
            const result = await createAdmin(username, password);
            console.log('✅ Tạo tài khoản admin thành công!');
            console.log('ID:', result.rows[0].id);
            console.log('Username:', username);
            rl.close();
            process.exit(0);
        } catch (error) {
            console.error('❌ Lỗi khi tạo tài khoản admin:', error.message);
            rl.close();
            process.exit(1);
        }
    });
}); 