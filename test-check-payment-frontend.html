<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Check Payment Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>Test Check Payment Frontend</h1>
    <p>Tool để test chức năng check payment với error handling cải thiện</p>

    <div class="test-section">
        <h3>1. Cấu hình Test</h3>
        <label>API URL: <input type="text" id="apiUrl" value="https://trustmove.vn" /></label><br>
        <label>Admin Token: <input type="password" id="adminToken" placeholder="Nhập admin token" /></label><br>
        <label>Order ID: <input type="number" id="orderId" value="156" /></label><br>
        <button onclick="saveConfig()">Lưu cấu hình</button>
    </div>

    <div class="test-section">
        <h3>2. Test API Check Payment</h3>
        <button onclick="testCheckPayment()">Test Check Payment</button>
        <button onclick="testWithInvalidToken()">Test với token không hợp lệ</button>
        <button onclick="testWithInvalidOrderId()">Test với Order ID không tồn tại</button>
        <button onclick="clearLog()">Xóa log</button>
        
        <div id="result"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let API_URL = 'https://trustmove.vn';
        let ADMIN_TOKEN = '';
        let ORDER_ID = 156;

        // Hàm log để hiển thị thông tin debug
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logEntry);
        }

        // Hàm hiển thị kết quả
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = isError ? 'error' : 'success';
            resultDiv.textContent = message;
        }

        // Lưu cấu hình
        function saveConfig() {
            API_URL = document.getElementById('apiUrl').value;
            ADMIN_TOKEN = document.getElementById('adminToken').value;
            ORDER_ID = parseInt(document.getElementById('orderId').value);
            
            log(`Cấu hình đã lưu: API=${API_URL}, OrderID=${ORDER_ID}`);
            showResult('Cấu hình đã được lưu');
        }

        // Hàm lấy header authentication
        function getAuthHeader() {
            return {
                'Authorization': `Bearer ${ADMIN_TOKEN}`,
                'Content-Type': 'application/json'
            };
        }

        // Test chức năng check payment với error handling cải thiện
        async function testCheckPayment() {
            if (!ADMIN_TOKEN) {
                showResult('Vui lòng nhập Admin Token', true);
                return;
            }

            log(`Bắt đầu test check payment cho đơn hàng ${ORDER_ID}`);
            
            try {
                // Gọi API check payment
                log(`Gọi API: POST ${API_URL}/api/admin/purchase-orders/${ORDER_ID}/check-payment`);
                
                const response = await fetch(`${API_URL}/api/admin/purchase-orders/${ORDER_ID}/check-payment`, {
                    method: 'POST',
                    headers: getAuthHeader()
                });

                log(`Response status: ${response.status} ${response.statusText}`);
                log(`Content-Type: ${response.headers.get('content-type')}`);

                if (response.ok) {
                    const result = await response.json();
                    log(`Response data: ${JSON.stringify(result, null, 2)}`);
                    showResult(`✅ Thành công: ${result.message}`);
                } else {
                    // Xử lý lỗi với error handling cải thiện
                    const contentType = response.headers.get('content-type');
                    let errorMessage = 'Có lỗi xảy ra khi kiểm tra thanh toán';
                    
                    log(`Lỗi response, content-type: ${contentType}`);
                    
                    try {
                        if (contentType && contentType.includes('application/json')) {
                            const error = await response.json();
                            errorMessage = error.message || errorMessage;
                            log(`Error JSON: ${JSON.stringify(error, null, 2)}`);
                        } else {
                            const errorText = await response.text();
                            log(`Error text (first 500 chars): ${errorText.substring(0, 500)}`);
                            
                            if (errorText.includes('<!DOCTYPE') || errorText.includes('<html')) {
                                errorMessage = `Lỗi server (${response.status}): Server trả về HTML error page thay vì JSON`;
                            } else {
                                errorMessage = errorText || errorMessage;
                            }
                        }
                    } catch (parseError) {
                        log(`Lỗi parse response: ${parseError.message}`);
                        errorMessage = `Lỗi server (${response.status}): Không thể xử lý phản hồi từ server`;
                    }
                    
                    showResult(`❌ ${errorMessage}`, true);
                }
            } catch (error) {
                log(`Exception: ${error.name} - ${error.message}`);
                
                let errorMessage = 'Có lỗi xảy ra khi kiểm tra thanh toán';
                
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = 'Không thể kết nối đến server. Kiểm tra kết nối mạng.';
                } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
                    errorMessage = 'Server trả về dữ liệu không hợp lệ.';
                } else if (error.message) {
                    errorMessage = `Lỗi: ${error.message}`;
                }
                
                showResult(`❌ ${errorMessage}`, true);
            }
        }

        // Test với token không hợp lệ
        async function testWithInvalidToken() {
            const originalToken = ADMIN_TOKEN;
            ADMIN_TOKEN = 'invalid_token_123';
            
            log('Test với token không hợp lệ');
            await testCheckPayment();
            
            ADMIN_TOKEN = originalToken;
        }

        // Test với Order ID không tồn tại
        async function testWithInvalidOrderId() {
            const originalOrderId = ORDER_ID;
            ORDER_ID = 999999;
            
            log('Test với Order ID không tồn tại');
            await testCheckPayment();
            
            ORDER_ID = originalOrderId;
        }

        // Xóa log
        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('result').textContent = '';
        }

        // Load cấu hình từ localStorage nếu có
        window.onload = function() {
            const savedToken = localStorage.getItem('adminToken');
            if (savedToken) {
                document.getElementById('adminToken').value = savedToken;
            }
        };

        // Lưu token vào localStorage khi thay đổi
        document.getElementById('adminToken').addEventListener('change', function() {
            localStorage.setItem('adminToken', this.value);
        });
    </script>
</body>
</html>
