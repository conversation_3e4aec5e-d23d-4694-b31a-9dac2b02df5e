const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
// <PERSON><PERSON><PERSON> bảo JWT_SECRET luôn có giá trị
const JWT_SECRET = process.env.JWT_SECRET || 'spx_tracking_secret_key_2024';
const { Pool } = require('pg');
require('dotenv').config();

// Kết nối database
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'tracking_db',
    password: 'England1@',
    port: 5432,
});

// Middleware xác thực admin token
const authenticateAdminToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.sendStatus(401);
    }

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
        if (err) {
            return res.sendStatus(403);
        }

        if (decoded.isAdmin) {
            req.admin = decoded;
            next();
        } else if (decoded.staffId) {
            req.staff = decoded;
            next();
        } else {
            return res.sendStatus(403);
        }
    });
};

// Middleware xác thực nhân viên token
const authenticateStaffToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.sendStatus(401);
    }

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
        if (err) {
            return res.sendStatus(403);
        }

        if (decoded.staffId) {
            req.staff = decoded;
            next();
        } else if (decoded.isAdmin) {
            req.admin = decoded;
            next();
        } else {
            return res.sendStatus(403);
        }
    });
};

// Đăng nhập admin
router.post('/login', async (req, res) => {
    const { username, password } = req.body;
    console.log('Đăng nhập với:', { username, passwordLength: password ? password.length : 0 });

    try {
        // Log thông tin request
        console.log('Thông tin request headers:', JSON.stringify(req.headers));

        // Kiểm tra tham số đầu vào
        if (!username || !password) {
            console.log('Thiếu tham số đầu vào');
            return res.status(400).json({ message: 'Thiếu tên đăng nhập hoặc mật khẩu' });
        }

        console.log('Đang truy vấn database...');
        const result = await pool.query(
            'SELECT id, username, password_hash FROM admin_users WHERE username = $1',
            [username]
        );

        console.log(`Kết quả tìm kiếm: Tìm thấy ${result.rows.length} user`);

        if (result.rows.length > 0) {
            const admin = result.rows[0];
            console.log(`Thông tin admin: ID=${admin.id}, Username=${admin.username}`);
            console.log('Password hash từ DB:', admin.password_hash);

            try {
                console.log('Bắt đầu so sánh mật khẩu với bcrypt...');
                const validPassword = await bcrypt.compare(password, admin.password_hash);
                console.log('Kết quả so sánh mật khẩu:', validPassword);

                if (validPassword) {
                    try {
                        console.log('Bắt đầu tạo JWT token...');
                        const tokenPayload = { id: admin.id, isAdmin: true };
                        console.log('Payload cho token:', tokenPayload);
                        console.log('JWT Secret có tồn tại:', !!process.env.JWT_SECRET);

                        const token = jwt.sign(tokenPayload, JWT_SECRET,
                            { expiresIn: '24h' }
                        );

                        console.log('Tạo token thành công, độ dài token:', token.length);

                        // Trả về response thành công
                        return res.json({
                            token,
                            admin: {
                                id: admin.id,
                                username: admin.username
                            }
                        });
                    } catch (tokenError) {
                        console.error('Lỗi khi tạo JWT token:', tokenError);
                        return res.status(500).json({
                            message: 'Lỗi server khi tạo token',
                            error: tokenError.message,
                            stack: tokenError.stack
                        });
                    }
                } else {
                    console.log('Sai mật khẩu');
                    return res.status(401).json({ message: 'Sai mật khẩu' });
                }
            } catch (bcryptError) {
                console.error('Lỗi bcrypt khi so sánh mật khẩu:', bcryptError);
                return res.status(500).json({
                    message: 'Lỗi server khi xác thực mật khẩu',
                    error: bcryptError.message,
                    stack: bcryptError.stack
                });
            }
        } else {
            console.log('Không tìm thấy user với username:', username);
            return res.status(401).json({ message: 'Không tìm thấy tài khoản này' });
        }
    } catch (error) {
        console.error('Lỗi chung trong quá trình đăng nhập:', error);
        return res.status(500).json({
            message: 'Lỗi server',
            error: error.message,
            stack: error.stack
        });
    }
});

// Kiểm tra phiên admin
router.post('/check-session', authenticateAdminToken, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT username FROM admin_users WHERE id = $1',
            [req.admin.id]
        );

        if (result.rows.length > 0) {
            res.json({ admin: result.rows[0] });
        } else {
            res.sendStatus(404);
        }
    } catch (error) {
        res.sendStatus(500);
    }
});

// Kiểm tra phiên nhân viên
router.post('/staff/check-session', authenticateStaffToken, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT username FROM staff WHERE id = $1',
            [req.staff.staffId]
        );

        if (result.rows.length > 0) {
            res.json({ staff: result.rows[0] });
        } else {
            res.sendStatus(404);
        }
    } catch (error) {
        res.sendStatus(500);
    }
});

// Đăng nhập nhân viên
router.post('/staff/login', async (req, res) => {
    const { username, password } = req.body;

    try {
        const result = await pool.query(
            'SELECT id, username, password_hash FROM staff WHERE username = $1',
            [username]
        );

        if (result.rows.length > 0) {
            const staff = result.rows[0];
            const validPassword = await bcrypt.compare(password, staff.password_hash);

            if (validPassword) {
                const token = jwt.sign({ staffId: staff.id }, JWT_SECRET,
                    { expiresIn: '24h' }
                );
                res.json({ token, staff: { id: staff.id, username: staff.username } });
            } else {
                res.sendStatus(401);
            }
        } else {
            res.sendStatus(401);
        }
    } catch (error) {
        res.sendStatus(500);
    }
});

// Tạo nhân viên mới
router.post('/staff', authenticateAdminToken, async (req, res) => {
    const { username, password, fullname, phone, email } = req.body;

    try {
        // Kiểm tra username đã tồn tại chưa
        const checkResult = await pool.query(
            'SELECT id FROM staff WHERE username = $1',
            [username]
        );

        if (checkResult.rows.length > 0) {
            return res.status(400).json({ message: 'Username đã tồn tại' });
        }

        // Mã hóa mật khẩu
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(password, salt);

        // Tạo nhân viên mới
        const result = await pool.query(
            'INSERT INTO staff (username, password_hash, fullname, phone, email) VALUES ($1, $2, $3, $4, $5) RETURNING id, username, fullname, phone, email, created_at',
            [username, passwordHash, fullname, phone, email]
        );

        res.status(201).json(result.rows[0]);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi tạo nhân viên' });
    }
});

// Tạo user mới (cho nhân viên)
router.post('/users', authenticateStaffToken, async (req, res) => {
    const { username, quota, staffId, phone, email, meta } = req.body;

    try {
        // Kiểm tra user đã tồn tại chưa
        const checkResult = await pool.query(
            'SELECT id FROM users WHERE username = $1',
            [username]
        );

        if (checkResult.rows.length > 0) {
            return res.status(400).json({ message: 'Username đã tồn tại' });
        }

        // Xác định staff_id
        let userStaffId = req.staff ? req.staff.staffId : null;

        // Nếu là admin và có chỉ định staffId
        if (req.admin && staffId) {
            // Kiểm tra nhân viên được chỉ định có tồn tại không
            const staffCheck = await pool.query('SELECT id FROM staff WHERE id = $1', [staffId]);
            if (staffCheck.rows.length > 0) {
                userStaffId = staffId;
            }
        }

        // Chuyển đổi quota sang số nếu có
        const userQuota = quota ? parseInt(quota) : 0;

        // Tạo user mới với staff_id, quota và các thông tin bổ sung
        const result = await pool.query(
            'INSERT INTO users (username, quota, staff_id, phone, email, meta) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id, username, quota, phone, email, meta, created_at, staff_id',
            [username, userQuota, userStaffId, phone || null, email || null, meta || null]
        );

        res.status(201).json(result.rows[0]);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi tạo user' });
    }
});

// Lấy danh sách users (cho nhân viên)
router.get('/users', authenticateStaffToken, async (req, res) => {
    try {
        // Nếu là admin, lấy tất cả users
        if (req.admin) {
            const result = await pool.query(`
                SELECT u.id, u.username, u.quota, u.phone, u.email, u.meta, u.created_at, u.staff_id, s.fullname as staff_name
                FROM users u
                LEFT JOIN staff s ON u.staff_id = s.id
                ORDER BY u.created_at DESC
            `);
            res.json(result.rows);
        } else {
            // Nếu là nhân viên, chỉ lấy users của mình
            const result = await pool.query(`
                SELECT u.id, u.username, u.quota, u.phone, u.email, u.meta, u.created_at, u.staff_id, s.fullname as staff_name
                FROM users u
                LEFT JOIN staff s ON u.staff_id = s.id
                WHERE u.staff_id = $1
                ORDER BY u.created_at DESC
            `, [req.staff.staffId]);
            res.json(result.rows);
        }
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách users' });
    }
});

// Xóa user (chỉ admin mới được xóa)
router.delete('/users/:id', authenticateAdminToken, async (req, res) => {
    try {
        // Xóa các tracking của user
        await pool.query('DELETE FROM tracking WHERE user_id = $1', [req.params.id]);

        // Xóa user
        const result = await pool.query(
            'DELETE FROM users WHERE id = $1 RETURNING id',
            [req.params.id]
        );

        if (result.rows.length > 0) {
            res.json({ message: 'User đã được xóa' });
        } else {
            res.status(404).json({ message: 'Không tìm thấy user' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa user' });
    }
});

// Tạo tracking mới
router.post('/tracking', authenticateStaffToken, async (req, res) => {
    try {
        const { number, status, userId, staffId, warehouseId, weight } = req.body;

        // Kiểm tra quyền hạn: Nhân viên chỉ có thể thêm tracking với trạng thái "Chờ nhận hàng"
        if (!req.admin && status !== 'chonhanhang') {
            return res.status(403).json({ message: 'Nhân viên chỉ có quyền thêm tracking với trạng thái "Chờ nhận hàng"' });
        }

        // Validate cân nặng nếu có
        let weightValue = null;
        if (weight !== undefined && weight !== null && weight !== '') {
            const weightNumber = parseFloat(weight);
            if (isNaN(weightNumber) || weightNumber <= 0) {
                return res.status(400).json({ message: 'Cân nặng phải là số dương' });
            }

            // Kiểm tra tối đa 3 số sau dấu phẩy
            if (weight.toString().includes('.') && weight.toString().split('.')[1].length > 3) {
                return res.status(400).json({ message: 'Cân nặng tối đa 3 số sau dấu phẩy' });
            }

            weightValue = weightNumber;
        }

        // Kiểm tra user có tồn tại không
        const userResult = await pool.query(
            'SELECT * FROM users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user này' });
        }

        // Kiểm tra tracking number đã tồn tại chưa
        const existingTracking = await pool.query(
            'SELECT * FROM tracking WHERE number = $1',
            [number]
        );

        if (existingTracking.rows.length > 0) {
            const currentStatus = existingTracking.rows[0].status;

            // Nếu chuyển từ hoàn thành sang nhập kho
            if (currentStatus === 'hoanthanh' && status === 'nhapkho') {
                return res.status(400).json({
                    message: 'Cảnh báo: Tracking number này đã hoàn thành. Việc chuyển sang trạng thái nhập kho có thể là nhầm lẫn. Vui lòng kiểm tra lại.'
                });
            }

            // Nếu chuyển từ nhập kho sang hoàn thành
            if (currentStatus === 'nhapkho' && status === 'hoanthanh') {
                // Cập nhật cả trạng thái và cân nặng nếu có
                if (weightValue !== null) {
                    await pool.query(
                        'UPDATE tracking SET status = $1, timestamp = CURRENT_TIMESTAMP, warehouse_id = $2, weight = $3 WHERE number = $4',
                        [status, warehouseId, weightValue, number]
                    );
                } else {
                    await pool.query(
                        'UPDATE tracking SET status = $1, timestamp = CURRENT_TIMESTAMP, warehouse_id = $2 WHERE number = $3',
                        [status, warehouseId, number]
                    );
                }
                return res.json({
                    message: 'Đã cập nhật trạng thái tracking từ nhập kho sang hoàn thành',
                    tracking: {
                        number,
                        status,
                        warehouse_id: warehouseId,
                        weight: weightValue,
                        timestamp: new Date()
                    }
                });
            }

            // Nếu trạng thái không thay đổi
            return res.status(400).json({
                message: 'Tracking number này đã tồn tại với trạng thái tương tự'
            });
        }

        // Nếu admin chỉ định nhân viên phụ trách mới
        if (req.admin && staffId) {
            // Kiểm tra nhân viên được chỉ định có tồn tại không
            const staffCheck = await pool.query('SELECT id FROM staff WHERE id = $1', [staffId]);
            if (staffCheck.rows.length > 0) {
                // Cập nhật staff_id cho user
                await pool.query(
                    'UPDATE users SET staff_id = $1 WHERE id = $2',
                    [staffId, userId]
                );
            }
        }

        // Tạo tracking mới với cân nặng
        const result = await pool.query(
            'INSERT INTO tracking (number, status, user_id, warehouse_id, weight) VALUES ($1, $2, $3, $4, $5) RETURNING *',
            [number, status, userId, warehouseId, weightValue]
        );

        // Nếu có cân nặng, ghi lịch sử khởi tạo cân nặng
        if (weightValue !== null) {
            const changedBy = req.admin ? req.admin.id : req.staff.staffId;
            await pool.query(
                'INSERT INTO tracking_weight_history (tracking_number, old_weight, new_weight, changed_by) VALUES ($1, $2, $3, $4)',
                [number, null, weightValue, changedBy]
            );
        }

        res.status(201).json(result.rows[0]);
    } catch (error) {
        console.error('Lỗi tạo tracking:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi tạo tracking' });
    }
});

// Lấy danh sách tracking
router.get('/tracking', authenticateStaffToken, async (req, res) => {
    try {
        // Nếu là admin, lấy tất cả tracking
        if (req.admin) {
            const result = await pool.query(`
                SELECT t.*, u.username, u.staff_id, s.fullname as staff_name, w.name as warehouse_name
                FROM tracking t
                JOIN users u ON t.user_id = u.id
                LEFT JOIN staff s ON u.staff_id = s.id
                LEFT JOIN warehouses w ON t.warehouse_id = w.id
                ORDER BY t.timestamp DESC
            `);
            res.json(result.rows);
        } else {
            // Nếu là nhân viên, chỉ lấy tracking của users do họ phụ trách
            const result = await pool.query(`
                SELECT t.*, u.username, u.staff_id, s.fullname as staff_name, w.name as warehouse_name
                FROM tracking t
                JOIN users u ON t.user_id = u.id
                LEFT JOIN staff s ON u.staff_id = s.id
                LEFT JOIN warehouses w ON t.warehouse_id = w.id
                WHERE u.staff_id = $1
                ORDER BY t.timestamp DESC
            `, [req.staff.staffId]);
            res.json(result.rows);
        }
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách tracking' });
    }
});

// Xóa tracking
router.delete('/tracking/:number', authenticateStaffToken, async (req, res) => {
    try {
        const { number } = req.params;

        // Kiểm tra tracking có tồn tại không và lấy thông tin user
        const trackingResult = await pool.query(
            'SELECT t.*, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        // Kiểm tra quyền xóa tracking
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xóa tracking này' });
        }

        // Xóa tracking
        await pool.query(
            'DELETE FROM tracking WHERE number = $1',
            [number]
        );

        res.json({ message: 'Xóa tracking thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa tracking' });
    }
});

// Lấy danh sách nhân viên
router.get('/staff', authenticateAdminToken, async (req, res) => {
    try {
        // Lấy thông tin nhân viên cùng với số lượng user đang quản lý
        const result = await pool.query(`
            SELECT s.id, s.username, s.fullname, s.phone, s.email, s.created_at,
                   COUNT(u.id) as user_count,
                   COALESCE(SUM(u.quota), 0) as total_quota
            FROM staff s
            LEFT JOIN users u ON s.id = u.staff_id
            GROUP BY s.id, s.username, s.fullname, s.phone, s.email, s.created_at
            ORDER BY s.created_at DESC
        `);
        res.json(result.rows);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách nhân viên' });
    }
});

// Route riêng cho đánh giá của nhân viên
router.get('/staff/my-evaluation', authenticateStaffToken, async (req, res) => {
    try {
        // Kiểm tra xem người dùng có phải là nhân viên không
        if (!req.staff || !req.staff.staffId) {
            return res.status(403).json({ message: 'Không có quyền truy cập' });
        }

        // Lấy thông tin đánh giá của nhân viên
        // Sử dụng logic từ route /staff/my-evaluation hiện tại
        let days = parseInt(req.query.days) || 30;
        let startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        let startDateStr = startDate.toISOString().split('T')[0];
        let currentDate = new Date();
        let currentDateStr = currentDate.toISOString().split('T')[0];

        if (req.query.startDate && req.query.endDate) {
            startDateStr = req.query.startDate;
            currentDateStr = req.query.endDate;
            startDate = new Date(startDateStr);
            currentDate = new Date(currentDateStr);
            const diffTime = Math.abs(currentDate - startDate);
            days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }

        const staffId = req.staff.staffId;

        // Lấy thông tin nhân viên
        const staffResult = await pool.query(`
            SELECT id, username, fullname
            FROM staff
            WHERE id = $1
        `, [staffId]);

        if (staffResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy thông tin nhân viên' });
        }

        const staff = staffResult.rows[0];

        // 1. Số lượng user thêm mới trong khoảng thời gian
        const newUsersResult = await pool.query(`
            SELECT COUNT(*) as count
            FROM users
            WHERE staff_id = $1
            AND created_at >= $2::timestamp
            AND created_at <= $3::timestamp + interval '1 day'
        `, [staffId, startDateStr, currentDateStr]);

        // 2. Số lượng user có tracking sau 2 tuần
        const twoWeeksDate = new Date(startDate);
        twoWeeksDate.setDate(twoWeeksDate.getDate() + 14);
        const twoWeeksDateStr = twoWeeksDate.toISOString().split('T')[0];

        const usersWithTrackingResult = await pool.query(`
            SELECT COUNT(DISTINCT u.id) as count
            FROM users u
            JOIN tracking t ON u.id = t.user_id
            WHERE u.staff_id = $1
            AND u.created_at >= $2::timestamp
            AND u.created_at <= $3::timestamp + interval '1 day'
            AND t.timestamp <= u.created_at + interval '14 days'
        `, [staffId, startDateStr, currentDateStr]);

        // 3. Số lượng user có sản lượng trên 300
        const usersAbove300Result = await pool.query(`
            SELECT COUNT(*) as count
            FROM users
            WHERE staff_id = $1
            AND quota > 300
            AND created_at >= $2::timestamp
            AND created_at <= $3::timestamp + interval '1 day'
        `, [staffId, startDateStr, currentDateStr]);

        // 4. Số lượng user có sản lượng trên 500
        const usersAbove500Result = await pool.query(`
            SELECT COUNT(*) as count
            FROM users
            WHERE staff_id = $1
            AND quota > 500
            AND created_at >= $2::timestamp
            AND created_at <= $3::timestamp + interval '1 day'
        `, [staffId, startDateStr, currentDateStr]);

        // Tổng số user hiện tại
        const totalUsersResult = await pool.query(`
            SELECT COUNT(*) as count
            FROM users
            WHERE staff_id = $1
        `, [staffId]);

        // Tổng số tracking hiện tại
        const totalTrackingResult = await pool.query(`
            SELECT COUNT(*) as count
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            WHERE u.staff_id = $1
        `, [staffId]);

        // Tổng sản lượng
        const totalQuotaResult = await pool.query(`
            SELECT COALESCE(SUM(quota), 0) as total
            FROM users
            WHERE staff_id = $1
        `, [staffId]);

        // Đóng gói kết quả
        const result = {
            staff_id: staff.id,
            username: staff.username,
            fullname: staff.fullname,
            evaluation: {
                new_users: {
                    count: parseInt(newUsersResult.rows[0].count),
                    target: 210,
                    percentage: Math.round((parseInt(newUsersResult.rows[0].count) / 210) * 100)
                },
                users_with_tracking: {
                    count: parseInt(usersWithTrackingResult.rows[0].count),
                    target: 117,
                    percentage: Math.round((parseInt(usersWithTrackingResult.rows[0].count) / 117) * 100)
                },
                users_above_300: {
                    count: parseInt(usersAbove300Result.rows[0].count),
                    target: 2,
                    percentage: Math.round((parseInt(usersAbove300Result.rows[0].count) / 2) * 100)
                },
                users_above_500: {
                    count: parseInt(usersAbove500Result.rows[0].count),
                    target: 1,
                    percentage: Math.round((parseInt(usersAbove500Result.rows[0].count) / 1) * 100)
                }
            },
            summary: {
                total_users: parseInt(totalUsersResult.rows[0].count),
                total_tracking: parseInt(totalTrackingResult.rows[0].count),
                total_quota: parseInt(totalQuotaResult.rows[0].total)
            },
            time_range: {
                days: days,
                start_date: startDateStr,
                end_date: currentDateStr
            }
        };

        res.json(result);
    } catch (error) {
        console.error('Lỗi lấy thông tin nhân viên:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy dữ liệu đánh giá nhân viên' });
    }
});

// Lấy thông tin chi tiết của một nhân viên
router.get('/staff/:id([0-9]+)', authenticateAdminToken, async (req, res) => {
    try {
        const staffId = req.params.id;

        const result = await pool.query(`
            SELECT s.id, s.username, s.fullname, s.phone, s.email, s.created_at,
                   COUNT(u.id) as user_count,
                   COALESCE(SUM(u.quota), 0) as total_quota
            FROM staff s
            LEFT JOIN users u ON s.id = u.staff_id
            WHERE s.id = $1
            GROUP BY s.id, s.username, s.fullname, s.phone, s.email, s.created_at
        `, [staffId]);

        if (result.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy nhân viên' });
        }

        res.json(result.rows[0]);
    } catch (error) {
        console.error('Lỗi lấy thông tin nhân viên:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy thông tin nhân viên' });
    }
});

// Lấy thống kê theo khoảng thời gian
router.get('/staff/stats', authenticateAdminToken, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;

        // Kiểm tra tham số đầu vào
        if (!startDate || !endDate) {
            return res.status(400).json({ message: 'Vui lòng cung cấp cả startDate và endDate' });
        }

        // Format chuỗi ngày giờ cho SQL query
        const startDateTime = `${startDate} 00:00:00`;
        const endDateTime = `${endDate} 23:59:59`;

        // Lấy thông tin nhân viên trong khoảng thời gian
        const staffQuery = `
            SELECT s.id, s.username, s.fullname, s.phone, s.email, s.created_at,
                COUNT(DISTINCT u.id) as user_count,
                COALESCE(SUM(u.quota), 0) as total_quota
            FROM staff s
            LEFT JOIN users u ON s.id = u.staff_id AND u.created_at BETWEEN $1 AND $2
            WHERE s.created_at <= $2
            GROUP BY s.id, s.username, s.fullname, s.phone, s.email, s.created_at
            ORDER BY s.created_at DESC
        `;

        // Lấy thông tin tracking trong khoảng thời gian
        const trackingQuery = `
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'nhapkho' THEN 1 END) as nhapkho,
                COUNT(CASE WHEN status = 'hoanthanh' THEN 1 END) as hoanthanh
            FROM tracking
            WHERE timestamp BETWEEN $1 AND $2
        `;

        // Thực hiện các truy vấn
        const staffResult = await pool.query(staffQuery, [startDateTime, endDateTime]);
        const trackingResult = await pool.query(trackingQuery, [startDateTime, endDateTime]);

        // Tạo đối tượng kết quả
        const result = {
            staff: staffResult.rows,
            trackingTotal: parseInt(trackingResult.rows[0].total) || 0,
            trackingNhapkho: parseInt(trackingResult.rows[0].nhapkho) || 0,
            trackingHoanthanh: parseInt(trackingResult.rows[0].hoanthanh) || 0,
            timeRange: {
                startDate,
                endDate
            }
        };

        res.json(result);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy thống kê theo thời gian' });
    }
});

// Xóa nhân viên
router.delete('/staff/:id', authenticateAdminToken, async (req, res) => {
    try {
        const result = await pool.query(
            'DELETE FROM staff WHERE id = $1 RETURNING id',
            [req.params.id]
        );

        if (result.rows.length > 0) {
            res.json({ message: 'Nhân viên đã được xóa' });
        } else {
            res.status(404).json({ message: 'Không tìm thấy nhân viên' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa nhân viên' });
    }
});

// Đổi mật khẩu nhân viên
router.put('/staff/:id/change-password', authenticateAdminToken, async (req, res) => {
    try {
        const { newPassword } = req.body;
        const staffId = req.params.id;

        // Kiểm tra input
        if (!newPassword) {
            return res.status(400).json({ message: 'Mật khẩu mới không được để trống' });
        }

        if (newPassword.length < 6) {
            return res.status(400).json({ message: 'Mật khẩu phải có ít nhất 6 ký tự' });
        }

        // Kiểm tra nhân viên có tồn tại không
        const staffResult = await pool.query(
            'SELECT * FROM staff WHERE id = $1',
            [staffId]
        );

        if (staffResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy nhân viên' });
        }

        // Mã hóa mật khẩu mới
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(newPassword, salt);

        // Cập nhật mật khẩu
        await pool.query(
            'UPDATE staff SET password_hash = $1 WHERE id = $2',
            [passwordHash, staffId]
        );

        res.json({ message: 'Đổi mật khẩu thành công' });
    } catch (error) {
        console.error('Lỗi đổi mật khẩu nhân viên:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi đổi mật khẩu' });
    }
});

// Phân công nhân viên phụ trách cho user
router.put('/users/:id/assign-staff', authenticateAdminToken, async (req, res) => {
    try {
        const { staffId } = req.body;
        const userId = req.params.id;

        // Kiểm tra user có tồn tại không
        const userResult = await pool.query(
            'SELECT * FROM users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user này' });
        }

        // Kiểm tra nhân viên có tồn tại không
        const staffResult = await pool.query(
            'SELECT * FROM staff WHERE id = $1',
            [staffId]
        );

        if (staffResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy nhân viên này' });
        }

        // Cập nhật staff_id cho user
        await pool.query(
            'UPDATE users SET staff_id = $1 WHERE id = $2',
            [staffId, userId]
        );

        res.json({ message: 'Đã phân công nhân viên phụ trách thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi phân công nhân viên' });
    }
});

// Phân công nhân viên phụ trách cho tracking
router.put('/tracking/:number/assign-staff', authenticateStaffToken, async (req, res) => {
    try {
        const { staffId } = req.body;
        const number = req.params.number;

        // Kiểm tra tracking có tồn tại không và lấy thông tin user
        const trackingResult = await pool.query(
            'SELECT t.*, u.id as user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        // Kiểm tra quyền phân công
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền phân công nhân viên cho tracking này' });
        }

        // Kiểm tra nhân viên có tồn tại không
        const staffResult = await pool.query(
            'SELECT * FROM staff WHERE id = $1',
            [staffId]
        );

        if (staffResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy nhân viên này' });
        }

        // Cập nhật staff_id cho user của tracking
        await pool.query(
            'UPDATE users SET staff_id = $1 WHERE id = $2',
            [staffId, trackingResult.rows[0].user_id]
        );

        // Khi phân công nhân viên cho một tracking, cập nhật toàn bộ tracking của user đó
        // Lưu ý: Điều này đã được xử lý tự động vì tracking liên kết với user_id,
        // và user mới được cập nhật staff_id ở trên

        res.json({
            message: 'Đã phân công nhân viên phụ trách thành công cho tất cả tracking của user này',
            user_id: trackingResult.rows[0].user_id,
            staff_id: staffId
        });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi phân công nhân viên' });
    }
});

// Cập nhật sản lượng dự tính cho user
router.put('/users/:id/update-quota', authenticateStaffToken, async (req, res) => {
    try {
        const { quota } = req.body;
        const userId = req.params.id;

        // Kiểm tra user có tồn tại không
        const userResult = await pool.query(
            'SELECT * FROM users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user này' });
        }

        // Kiểm tra quyền cập nhật (chỉ admin hoặc nhân viên phụ trách mới được cập nhật)
        const user = userResult.rows[0];
        if (!req.admin && user.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền cập nhật sản lượng dự tính cho user này' });
        }

        // Chuyển đổi quota sang số nếu có
        const userQuota = quota ? parseInt(quota) : 0;

        // Cập nhật sản lượng dự tính cho user
        await pool.query(
            'UPDATE users SET quota = $1 WHERE id = $2',
            [userQuota, userId]
        );

        res.json({
            message: 'Đã cập nhật sản lượng dự tính thành công',
            user_id: userId,
            quota: userQuota
        });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật sản lượng dự tính' });
    }
});

// Cập nhật thông tin liên hệ cho user
router.put('/users/:id/update-info', authenticateStaffToken, async (req, res) => {
    try {
        const { phone, email, meta } = req.body;
        const userId = req.params.id;

        // Kiểm tra user có tồn tại không
        const userResult = await pool.query(
            'SELECT * FROM users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user này' });
        }

        // Kiểm tra quyền cập nhật (chỉ admin hoặc nhân viên phụ trách mới được cập nhật)
        const user = userResult.rows[0];
        if (!req.admin && user.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền cập nhật thông tin cho user này' });
        }

        // Cập nhật thông tin liên hệ cho user
        await pool.query(
            'UPDATE users SET phone = $1, email = $2, meta = $3 WHERE id = $4',
            [phone || null, email || null, meta || null, userId]
        );

        // Lấy thông tin user đã cập nhật
        const updatedUser = await pool.query(
            'SELECT id, username, quota, phone, email, meta, created_at, staff_id FROM users WHERE id = $1',
            [userId]
        );

        res.json({
            message: 'Đã cập nhật thông tin liên hệ thành công',
            user: updatedUser.rows[0]
        });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật thông tin liên hệ' });
    }
});

// Lấy thông tin đánh giá nhân viên
router.get('/staff/evaluation', authenticateAdminToken, async (req, res) => {
    try {
        let startDate, currentDate, days;
        let startDateStr, currentDateStr;

        // Kiểm tra xem có tham số startDate và endDate hay không
        if (req.query.startDate && req.query.endDate) {
            startDateStr = req.query.startDate;
            currentDateStr = req.query.endDate;

            startDate = new Date(startDateStr);
            currentDate = new Date(currentDateStr);

            // Tính số ngày giữa hai ngày
            const diffTime = Math.abs(currentDate - startDate);
            days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        } else {
            // Sử dụng tham số days nếu không có startDate và endDate
            days = parseInt(req.query.days) || 30;

            // Tính toán ngày bắt đầu (days ngày trước)
            startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            startDateStr = startDate.toISOString().split('T')[0];

            // Ngày hiện tại
            currentDate = new Date();
            currentDateStr = currentDate.toISOString().split('T')[0];
        }

        // Lấy danh sách tất cả nhân viên
        const staffResult = await pool.query(`
            SELECT id, username, fullname
            FROM staff
            ORDER BY fullname
        `);

        const result = [];

        // Xử lý từng nhân viên
        for (const staff of staffResult.rows) {
            // 1. Số lượng user thêm mới trong khoảng thời gian
            const newUsersResult = await pool.query(`
                SELECT COUNT(*) as count
                FROM users
                WHERE staff_id = $1
                AND created_at >= $2::timestamp
                AND created_at <= $3::timestamp + interval '1 day'
            `, [staff.id, startDateStr, currentDateStr]);

            // 2. Số lượng user có tracking sau 2 tuần
            const twoWeeksDate = new Date(startDate);
            twoWeeksDate.setDate(twoWeeksDate.getDate() + 14);
            const twoWeeksDateStr = twoWeeksDate.toISOString().split('T')[0];

            const usersWithTrackingResult = await pool.query(`
                SELECT COUNT(DISTINCT u.id) as count
                FROM users u
                JOIN tracking t ON u.id = t.user_id
                WHERE u.staff_id = $1
                AND u.created_at >= $2::timestamp
                AND u.created_at <= $3::timestamp + interval '1 day'
                AND t.timestamp <= u.created_at + interval '14 days'
            `, [staff.id, startDateStr, currentDateStr]);

            // 3. Số lượng user có sản lượng trên 300
            const usersAbove300Result = await pool.query(`
                SELECT COUNT(*) as count
                FROM users
                WHERE staff_id = $1
                AND quota > 300
                AND created_at >= $2::timestamp
                AND created_at <= $3::timestamp + interval '1 day'
            `, [staff.id, startDateStr, currentDateStr]);

            // 4. Số lượng user có sản lượng trên 500
            const usersAbove500Result = await pool.query(`
                SELECT COUNT(*) as count
                FROM users
                WHERE staff_id = $1
                AND quota > 500
                AND created_at >= $2::timestamp
                AND created_at <= $3::timestamp + interval '1 day'
            `, [staff.id, startDateStr, currentDateStr]);

            // Thêm thông tin đánh giá cho nhân viên
            result.push({
                staff_id: staff.id,
                username: staff.username,
                fullname: staff.fullname,
                evaluation: {
                    new_users: {
                        count: parseInt(newUsersResult.rows[0].count),
                        target: 210,
                        percentage: Math.round((parseInt(newUsersResult.rows[0].count) / 210) * 100)
                    },
                    users_with_tracking: {
                        count: parseInt(usersWithTrackingResult.rows[0].count),
                        target: 117,
                        percentage: Math.round((parseInt(usersWithTrackingResult.rows[0].count) / 117) * 100)
                    },
                    users_above_300: {
                        count: parseInt(usersAbove300Result.rows[0].count),
                        target: 2,
                        percentage: Math.round((parseInt(usersAbove300Result.rows[0].count) / 2) * 100)
                    },
                    users_above_500: {
                        count: parseInt(usersAbove500Result.rows[0].count),
                        target: 1,
                        percentage: Math.round((parseInt(usersAbove500Result.rows[0].count) / 1) * 100)
                    }
                },
                time_range: {
                    days: days,
                    start_date: startDateStr,
                    end_date: currentDateStr
                }
            });
        }

        res.json(result);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy dữ liệu đánh giá nhân viên' });
    }
});

// Lấy danh sách kho
router.get('/warehouses', authenticateStaffToken, async (req, res) => {
    try {
        const result = await pool.query(`
            SELECT * FROM warehouses
            ORDER BY name ASC
        `);
        res.json(result.rows);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách kho' });
    }
});

// Thêm kho mới (chỉ admin)
router.post('/warehouses', authenticateAdminToken, async (req, res) => {
    try {
        const { name, address, delivery_time } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Tên kho là bắt buộc' });
        }

        const result = await pool.query(
            'INSERT INTO warehouses (name, address, delivery_time) VALUES ($1, $2, $3) RETURNING *',
            [name, address || null, delivery_time || 14]
        );

        res.status(201).json(result.rows[0]);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi thêm kho mới' });
    }
});

// Cập nhật thông tin kho (chỉ admin)
router.put('/warehouses/:id', authenticateAdminToken, async (req, res) => {
    try {
        const { name, address, delivery_time } = req.body;
        const warehouseId = req.params.id;

        if (!name) {
            return res.status(400).json({ message: 'Tên kho là bắt buộc' });
        }

        // Kiểm tra kho có tồn tại không
        const checkResult = await pool.query(
            'SELECT * FROM warehouses WHERE id = $1',
            [warehouseId]
        );

        if (checkResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy kho này' });
        }

        // Cập nhật thông tin kho
        const result = await pool.query(
            'UPDATE warehouses SET name = $1, address = $2, delivery_time = $3 WHERE id = $4 RETURNING *',
            [name, address || null, delivery_time || checkResult.rows[0].delivery_time || 14, warehouseId]
        );

        res.json(result.rows[0]);
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật thông tin kho' });
    }
});

// Xóa kho (chỉ admin)
router.delete('/warehouses/:id', authenticateAdminToken, async (req, res) => {
    try {
        const warehouseId = req.params.id;

        // Kiểm tra kho có tồn tại không
        const checkResult = await pool.query(
            'SELECT * FROM warehouses WHERE id = $1',
            [warehouseId]
        );

        if (checkResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy kho này' });
        }

        // Kiểm tra xem có tracking nào đang sử dụng kho này không
        const trackingResult = await pool.query(
            'SELECT COUNT(*) FROM tracking WHERE warehouse_id = $1',
            [warehouseId]
        );

        if (parseInt(trackingResult.rows[0].count) > 0) {
            return res.status(400).json({
                message: 'Không thể xóa kho này vì đang có tracking sử dụng. Vui lòng cập nhật các tracking trước khi xóa.'
            });
        }

        // Xóa kho
        await pool.query(
            'DELETE FROM warehouses WHERE id = $1',
            [warehouseId]
        );

        res.json({ message: 'Đã xóa kho thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa kho' });
    }
});

// Cập nhật kho cho tracking
router.put('/tracking/:number/update-warehouse', authenticateStaffToken, async (req, res) => {
    try {
        const { warehouseId } = req.body;
        const number = req.params.number;

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.*, u.id as user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        // Kiểm tra kho có tồn tại không
        const warehouseResult = await pool.query(
            'SELECT * FROM warehouses WHERE id = $1',
            [warehouseId]
        );

        if (warehouseResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy kho này' });
        }

        // Kiểm tra quyền cập nhật (admin hoặc nhân viên phụ trách)
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền cập nhật kho cho tracking này' });
        }

        // Cập nhật kho cho tracking
        await pool.query(
            'UPDATE tracking SET warehouse_id = $1 WHERE number = $2',
            [warehouseId, number]
        );

        res.json({
            message: 'Đã cập nhật kho cho tracking thành công',
            tracking_number: number,
            warehouse_id: warehouseId
        });
    } catch (error) {
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật kho cho tracking' });
    }
});

// Cập nhật trạng thái tracking
router.put('/tracking/:number/update-status', authenticateStaffToken, async (req, res) => {
    try {
        const { status } = req.body;
        const number = req.params.number;

        // Kiểm tra status có hợp lệ không
        const validStatuses = ['chonhanhang', 'nhapkho', 'dangvevn', 'nhapkhovn', 'hoanthanh'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ message: 'Trạng thái không hợp lệ' });
        }

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.*, u.id as user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        // Kiểm tra quyền cập nhật (admin hoặc nhân viên phụ trách)
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền cập nhật trạng thái cho tracking này' });
        }

        // Kiểm tra giới hạn quyền: Nhân viên chỉ có thể cập nhật trạng thái thành "Chờ nhận hàng"
        if (!req.admin && status !== 'chonhanhang') {
            return res.status(403).json({ message: 'Nhân viên chỉ có quyền cập nhật trạng thái thành "Chờ nhận hàng"' });
        }

        // Kiểm tra thứ tự trạng thái
        const statusOrder = {
            'chonhanhang': 1,
            'nhapkho': 2,
            'dangvevn': 3,
            'nhapkhovn': 4,
            'hoanthanh': 5
        };

        const currentStatusOrder = statusOrder[tracking.status] || 0;
        const newStatusOrder = statusOrder[status] || 0;

        // Nếu đang chuyển từ trạng thái cao hơn xuống trạng thái thấp hơn, hiển thị cảnh báo
        if (newStatusOrder < currentStatusOrder) {
            // Vẫn cho phép cập nhật nhưng trả về thông báo cảnh báo
            await pool.query(
                'UPDATE tracking SET status = $1, timestamp = CURRENT_TIMESTAMP WHERE number = $2',
                [status, number]
            );

            // Ghi lại lịch sử thay đổi trạng thái với staff_id
            if (req.staff) {
                await pool.query(
                    'INSERT INTO tracking_history (tracking_id, tracking_number, status, changed_by) VALUES ($1, $2, $3, $4)',
                    [tracking.id, number, status, req.staff.staffId]
                );
            }

            return res.json({
                message: 'Đã cập nhật trạng thái tracking thành công, nhưng lưu ý rằng bạn đang chuyển từ trạng thái cao hơn xuống trạng thái thấp hơn.',
                tracking_number: number,
                status: status,
                previous_status: tracking.status
            });
        }

        // Cập nhật trạng thái cho tracking
        await pool.query(
            'UPDATE tracking SET status = $1, timestamp = CURRENT_TIMESTAMP WHERE number = $2',
            [status, number]
        );

        // Ghi lại lịch sử thay đổi trạng thái với staff_id
        if (req.staff) {
            await pool.query(
                'INSERT INTO tracking_history (tracking_id, tracking_number, status, changed_by) VALUES ($1, $2, $3, $4)',
                [tracking.id, number, status, req.staff.staffId]
            );
        }

        res.json({
            message: 'Đã cập nhật trạng thái tracking thành công',
            tracking_number: number,
            status: status
        });
    } catch (error) {
        console.error('Lỗi cập nhật trạng thái tracking:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật trạng thái tracking' });
    }
});

// Lấy thông tin chi tiết của một tracking
router.get('/tracking/:number/details', authenticateStaffToken, async (req, res) => {
    try {
        const number = req.params.number;

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.*, u.id as user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            // Kiểm tra trong bảng undeclared_tracking
            const undeclaredResult = await pool.query(
                'SELECT * FROM undeclared_tracking WHERE number = $1 ORDER BY created_at DESC LIMIT 1',
                [number]
            );

            if (undeclaredResult.rows.length === 0) {
                return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
            }

            // Trả về thông tin từ undeclared_tracking
            return res.json(undeclaredResult.rows[0]);
        }

        // Kiểm tra quyền xem (admin hoặc nhân viên phụ trách)
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem thông tin tracking này' });
        }

        res.json(tracking);
    } catch (error) {
        console.error('Lỗi lấy thông tin chi tiết tracking:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy thông tin chi tiết tracking' });
    }
});

// Lấy lịch sử trạng thái của một tracking
router.get('/tracking/:number/history', authenticateStaffToken, async (req, res) => {
    try {
        const number = req.params.number;

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.*, u.id as user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        // Kiểm tra quyền xem (admin hoặc nhân viên phụ trách)
        const tracking = trackingResult.rows[0];
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem lịch sử tracking này' });
        }

        // Lấy lịch sử trạng thái
        const historyResult = await pool.query(`
            SELECT h.*, s.fullname as staff_name
            FROM tracking_history h
            LEFT JOIN staff s ON h.changed_by = s.id
            WHERE h.tracking_number = $1
            ORDER BY h.changed_at DESC
        `, [number]);

        res.json(historyResult.rows);
    } catch (error) {
        console.error('Lỗi lấy lịch sử tracking:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy lịch sử tracking' });
    }
});

// API endpoint để kiểm tra các điều kiện cảnh báo tracking
router.get('/tracking/alerts', authenticateAdminToken, async (req, res) => {
    try {
        // Lấy ngày hiện tại
        const currentDate = new Date();

        // Mảng chứa các cảnh báo
        const alerts = [];

        // 1. Tracking có trạng thái "Chờ nhận hàng" đến "Đã nhập kho" quá 10 ngày
        const chonhanhangAlerts = await pool.query(`
            SELECT t.*, u.username, th.changed_at
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            JOIN (
                SELECT tracking_id, MIN(changed_at) as changed_at
                FROM tracking_history
                WHERE status = 'chonhanhang'
                GROUP BY tracking_id
            ) th ON t.id = th.tracking_id
            WHERE t.status = 'chonhanhang'
            AND th.changed_at < NOW() - INTERVAL '10 days'
        `);

        chonhanhangAlerts.rows.forEach(alert => {
            const daysPassed = Math.floor((currentDate - new Date(alert.changed_at)) / (1000 * 60 * 60 * 24));
            alerts.push({
                id: alert.id,
                number: alert.number,
                status: alert.status,
                username: alert.username,
                changed_at: alert.changed_at,
                days_passed: daysPassed,
                alert_type: 'chonhanhang_overdue',
                message: `Tracking ${alert.number} đã ở trạng thái "Chờ nhận hàng" quá 10 ngày (${daysPassed} ngày)`
            });
        });

        // 2. Tracking có trạng thái "Đã nhập kho" đến "Đang về VN" quá 5 ngày
        const nhapkhoAlerts = await pool.query(`
            SELECT t.*, u.username, th.changed_at
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            JOIN (
                SELECT tracking_id, MIN(changed_at) as changed_at
                FROM tracking_history
                WHERE status = 'nhapkho'
                GROUP BY tracking_id
            ) th ON t.id = th.tracking_id
            WHERE t.status = 'nhapkho'
            AND th.changed_at < NOW() - INTERVAL '5 days'
        `);

        nhapkhoAlerts.rows.forEach(alert => {
            const daysPassed = Math.floor((currentDate - new Date(alert.changed_at)) / (1000 * 60 * 60 * 24));
            alerts.push({
                id: alert.id,
                number: alert.number,
                status: alert.status,
                username: alert.username,
                changed_at: alert.changed_at,
                days_passed: daysPassed,
                alert_type: 'nhapkho_overdue',
                message: `Tracking ${alert.number} đã ở trạng thái "Đã nhập kho" quá 5 ngày (${daysPassed} ngày)`
            });
        });

        // 3. Tracking có trạng thái "Đang về VN" đến "Đã nhập kho VN" quá ngày "Dự kiến về kho VN"
        const dangvevnAlerts = await pool.query(`
            SELECT t.*, u.username, th.changed_at, w.delivery_time
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            JOIN warehouses w ON t.warehouse_id = w.id
            JOIN (
                SELECT tracking_id, MIN(changed_at) as changed_at
                FROM tracking_history
                WHERE status = 'dangvevn'
                GROUP BY tracking_id
            ) th ON t.id = th.tracking_id
            WHERE t.status = 'dangvevn'
            AND th.changed_at + (w.delivery_time * INTERVAL '1 day') < NOW()
        `);

        dangvevnAlerts.rows.forEach(alert => {
            const expectedDate = new Date(alert.changed_at);
            expectedDate.setDate(expectedDate.getDate() + alert.delivery_time);
            const daysPassed = Math.floor((currentDate - expectedDate) / (1000 * 60 * 60 * 24));

            alerts.push({
                id: alert.id,
                number: alert.number,
                status: alert.status,
                username: alert.username,
                changed_at: alert.changed_at,
                days_passed: daysPassed,
                alert_type: 'dangvevn_overdue',
                message: `Tracking ${alert.number} đã quá ngày dự kiến về kho VN (${daysPassed} ngày)`
            });
        });

        // 4. Tracking có trạng thái "Đã nhập kho VN" đến "Hoàn thành" quá 3 ngày
        const nhapkhovnAlerts = await pool.query(`
            SELECT t.*, u.username, th.changed_at
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            JOIN (
                SELECT tracking_id, MIN(changed_at) as changed_at
                FROM tracking_history
                WHERE status = 'nhapkhovn'
                GROUP BY tracking_id
            ) th ON t.id = th.tracking_id
            WHERE t.status = 'nhapkhovn'
            AND th.changed_at < NOW() - INTERVAL '3 days'
        `);

        nhapkhovnAlerts.rows.forEach(alert => {
            const daysPassed = Math.floor((currentDate - new Date(alert.changed_at)) / (1000 * 60 * 60 * 24));
            alerts.push({
                id: alert.id,
                number: alert.number,
                status: alert.status,
                username: alert.username,
                changed_at: alert.changed_at,
                days_passed: daysPassed,
                alert_type: 'nhapkhovn_overdue',
                message: `Tracking ${alert.number} đã ở trạng thái "Đã nhập kho VN" quá 3 ngày (${daysPassed} ngày)`
            });
        });

        // Trả về kết quả
        res.json({
            total_alerts: alerts.length,
            alerts: alerts
        });
    } catch (error) {
        console.error('Lỗi khi kiểm tra cảnh báo tracking:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi kiểm tra cảnh báo tracking' });
    }
});

// API endpoints cho chức năng hóa đơn

// Lấy danh sách tracking chưa hoàn thành của một user với hỗ trợ tìm kiếm và lọc
router.get('/invoices/user-tracking/:userId', authenticateAdminToken, async (req, res) => {
    try {
        const userId = req.params.userId;
        const { search, status, warehouse_id, date_from, date_to } = req.query;

        // Xây dựng câu query với các điều kiện lọc
        let query = `
            SELECT t.id, t.number, t.status, t.timestamp, t.user_id, t.warehouse_id,
                   t.weight,
                   u.username, w.name as warehouse_name, w.address as warehouse_address
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            LEFT JOIN warehouses w ON t.warehouse_id = w.id
            WHERE t.user_id = $1 AND t.status != 'hoanthanh'
        `;

        const params = [userId];
        let paramIndex = 2;

        // Thêm điều kiện tìm kiếm theo tracking number
        if (search && search.trim()) {
            query += ` AND t.number ILIKE $${paramIndex}`;
            params.push(`%${search.trim()}%`);
            paramIndex++;
        }

        // Thêm điều kiện lọc theo trạng thái
        if (status && status.trim()) {
            query += ` AND t.status = $${paramIndex}`;
            params.push(status.trim());
            paramIndex++;
        }

        // Thêm điều kiện lọc theo kho
        if (warehouse_id && !isNaN(parseInt(warehouse_id))) {
            query += ` AND t.warehouse_id = $${paramIndex}`;
            params.push(parseInt(warehouse_id));
            paramIndex++;
        }

        // Thêm điều kiện lọc theo ngày bắt đầu
        if (date_from) {
            query += ` AND t.timestamp >= $${paramIndex}`;
            params.push(date_from);
            paramIndex++;
        }

        // Thêm điều kiện lọc theo ngày kết thúc
        if (date_to) {
            query += ` AND t.timestamp <= $${paramIndex}`;
            params.push(date_to + ' 23:59:59'); // Bao gồm cả ngày kết thúc
            paramIndex++;
        }

        query += ` ORDER BY t.timestamp DESC`;

        const result = await pool.query(query, params);

        res.json(result.rows);
    } catch (error) {
        console.error('Lỗi khi lấy danh sách tracking cho hóa đơn:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách tracking' });
    }
});

// Lấy danh sách kho để sử dụng cho filter
router.get('/warehouses/list', authenticateAdminToken, async (req, res) => {
    try {
        const result = await pool.query(`
            SELECT id, name, address
            FROM warehouses
            ORDER BY name
        `);
        res.json(result.rows);
    } catch (error) {
        console.error('Lỗi khi lấy danh sách kho:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách kho', error: error.message });
    }
});

// Tạo hóa đơn mới - ENDPOINT CŨ, ĐÃ ĐƯỢC THAY THẾ BỞI api/invoice.js
// router.post('/invoices', authenticateAdminToken, async (req, res) => {

// Lấy danh sách hóa đơn
router.get('/invoices', authenticateAdminToken, async (req, res) => {
    try {
        const result = await pool.query(`
            SELECT i.id, i.user_id, u.username, i.total_amount, i.note, i.created_at, i.status,
                   i.invoice_code, i.payment_method, i.payment_status
            FROM invoices i
            JOIN users u ON i.user_id = u.id
            ORDER BY i.created_at DESC
        `);

        res.json(result.rows);
    } catch (error) {
        console.error('Lỗi khi lấy danh sách hóa đơn:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách hóa đơn' });
    }
});

// Lấy chi tiết hóa đơn - ENDPOINT CŨ, ĐÃ ĐƯỢC THAY THẾ BỞI api/invoice.js
// Đã xóa để tránh conflict với endpoint mới trong api/invoice.js
// router.get('/invoices/:id', authenticateAdminToken, async (req, res) => {

// Cập nhật trạng thái hóa đơn
router.put('/invoices/:id/status', authenticateAdminToken, async (req, res) => {
    try {
        const { status } = req.body;

        await pool.query(`
            UPDATE invoices
            SET status = $1
            WHERE id = $2
        `, [status, req.params.id]);

        res.json({ message: 'Cập nhật trạng thái hóa đơn thành công' });
    } catch (error) {
        console.error('Lỗi khi cập nhật trạng thái hóa đơn:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật trạng thái hóa đơn' });
    }
});

// Xóa hóa đơn
router.delete('/invoices/:id', authenticateAdminToken, async (req, res) => {
    const client = await pool.connect();

    try {
        await client.query('BEGIN');

        // Xóa các mục trong hóa đơn
        await client.query('DELETE FROM invoice_items WHERE invoice_id = $1', [req.params.id]);

        // Xóa hóa đơn
        await client.query('DELETE FROM invoices WHERE id = $1', [req.params.id]);

        await client.query('COMMIT');

        res.json({ message: 'Xóa hóa đơn thành công' });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi xóa hóa đơn:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa hóa đơn' });
    } finally {
        client.release();
    }
});

// Cập nhật cân nặng cho tracking
router.put('/tracking/:number/weight', authenticateStaffToken, async (req, res) => {
    try {
        const number = req.params.number;
        const { weight } = req.body;

        // Validate cân nặng
        if (weight === undefined || weight === null || weight === '') {
            return res.status(400).json({ message: 'Vui lòng nhập cân nặng' });
        }

        const weightNumber = parseFloat(weight);
        if (isNaN(weightNumber) || weightNumber <= 0) {
            return res.status(400).json({ message: 'Cân nặng phải là số dương' });
        }

        // Kiểm tra tối đa 3 số sau dấu phẩy
        if (weight.toString().includes('.') && weight.toString().split('.')[1].length > 3) {
            return res.status(400).json({ message: 'Cân nặng tối đa 3 số sau dấu phẩy' });
        }

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.id, t.weight, t.user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        const tracking = trackingResult.rows[0];

        // Kiểm tra quyền cập nhật (admin hoặc nhân viên phụ trách)
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền cập nhật cân nặng cho tracking này' });
        }

        // Xác định người thực hiện thay đổi
        const changedBy = req.admin ? req.admin.id : req.staff.staffId;

        // Lưu lịch sử thay đổi cân nặng
        await pool.query(
            'INSERT INTO tracking_weight_history (tracking_number, old_weight, new_weight, changed_by) VALUES ($1, $2, $3, $4)',
            [number, tracking.weight, weightNumber, changedBy]
        );

        // Cập nhật cân nặng mới
        await pool.query(
            'UPDATE tracking SET weight = $1 WHERE number = $2',
            [weightNumber, number]
        );

        res.json({
            message: 'Cập nhật cân nặng thành công',
            weight: weightNumber,
            previousWeight: tracking.weight
        });
    } catch (error) {
        console.error('Lỗi cập nhật cân nặng:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật cân nặng' });
    }
});

// Lấy lịch sử thay đổi cân nặng
router.get('/tracking/:number/weight-history', authenticateStaffToken, async (req, res) => {
    try {
        const number = req.params.number;

        // Kiểm tra tracking có tồn tại không
        const trackingResult = await pool.query(
            'SELECT t.id, t.user_id, u.staff_id FROM tracking t JOIN users u ON t.user_id = u.id WHERE t.number = $1',
            [number]
        );

        if (trackingResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy tracking number này' });
        }

        const tracking = trackingResult.rows[0];

        // Kiểm tra quyền xem (admin hoặc nhân viên phụ trách)
        if (!req.admin && tracking.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem lịch sử cân nặng cho tracking này' });
        }

        // Lấy lịch sử thay đổi cân nặng
        const result = await pool.query(
            `SELECT h.*,
                    COALESCE(s.fullname, a.username) as changer_name,
                    CASE WHEN s.id IS NOT NULL THEN 'staff' ELSE 'admin' END as changer_type
             FROM tracking_weight_history h
             LEFT JOIN staff s ON h.changed_by = s.id
             LEFT JOIN admin_users a ON h.changed_by = a.id AND s.id IS NULL
             WHERE h.tracking_number = $1
             ORDER BY h.changed_at DESC`,
            [number]
        );

        res.json(result.rows);
    } catch (error) {
        console.error('Lỗi lấy lịch sử cân nặng:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy lịch sử cân nặng' });
    }
});

/**
 * Lấy thông tin productivity của một user
 * Trả về sản lượng 30 ngày và tổng sản lượng
 */
router.get('/users/:username/productivity', authenticateStaffToken, async (req, res) => {
    try {
        const { username } = req.params;

        // Kiểm tra user có tồn tại không
        const userResult = await pool.query(
            'SELECT id, username, quota, staff_id, created_at FROM users WHERE username = $1',
            [username]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user này' });
        }

        const user = userResult.rows[0];

        // Kiểm tra quyền truy cập (admin hoặc nhân viên phụ trách)
        if (!req.admin && user.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem thông tin productivity của user này' });
        }

        // Tính sản lượng 30 ngày qua (tổng cân nặng tracking hoàn thành)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const last30DaysResult = await pool.query(`
            SELECT COALESCE(SUM(t.weight), 0) as total
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            WHERE u.username = $1
            AND t.status = 'hoanthanh'
            AND t.timestamp >= $2
        `, [username, thirtyDaysAgo]);

        // Tính tổng sản lượng (tổng cân nặng tất cả tracking hoàn thành)
        const totalProductivityResult = await pool.query(`
            SELECT COALESCE(SUM(t.weight), 0) as total
            FROM tracking t
            JOIN users u ON t.user_id = u.id
            WHERE u.username = $1
            AND t.status = 'hoanthanh'
        `, [username]);

        // Trả về dữ liệu productivity
        res.json({
            username: user.username,
            last30Days: parseFloat(last30DaysResult.rows[0].total) || 0,
            total: parseFloat(totalProductivityResult.rows[0].total) || 0,
            staff_id: user.staff_id,
            calculated_at: new Date().toISOString()
        });

    } catch (error) {
        console.error('Lỗi khi lấy thông tin productivity:', error);
        res.status(500).json({
            message: 'Có lỗi xảy ra khi lấy thông tin productivity',
            error: error.message
        });
    }
});

module.exports = router;